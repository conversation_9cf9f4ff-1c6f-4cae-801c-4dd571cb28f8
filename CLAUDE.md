# CLAUDE.md

**重要说明**: 
- 请用中文与我交流
- 所有生成的文档和报告都使用当前系统时间 (2025-07-14)
- 本文件为 Claude Code (claude.ai/code) 提供项目工作指南

**最后更新**: 2025年7月14日

## 项目概述

MasteryOS (万时通) 是基于"一万小时定律"的智能技能发展和追踪系统。这是一个综合学习平台，结合AI辅助、社交学习和游戏化机制，帮助用户在任何技能领域实现精通。

## 当前项目状态 🚀

### ✅ 已完成的核心功能 (2025-07-14)

**1. 完整的目录结构重组**
- 统一 monorepo 架构，清晰的 `apps/` 目录组织
- 移除重复和混乱的目录结构
- 标准化的 `infrastructure/docker/` 配置

**2. Flutter 移动应用基础**
- 完整的导航系统 (底部导航栏 + go_router)
- 四个主要页面: 首页、文档、学习、个人资料
- 已解决 TODO 注释，实现个人资料页面导航
- 响应式 UI 设计，支持 Web 开发

**3. 开发环境配置**
- Docker 混合开发环境 (PostgreSQL + Redis)
- 多种开发脚本支持 (Flutter、混合模式、独立模式)
- 完整的开发工具链配置

**4. 项目清理和优化**
- 移除不必要的 `.devcontainer/` 配置
- 清理过时文档和配置文件
- 简化项目结构，降低维护复杂度

### 📁 当前目录结构

```
masteryos/
├── apps/                           # ✅ 应用程序目录
│   ├── mobile/                     # ✅ Flutter 移动应用 (已实现导航)
│   ├── admin-spa/                  # ✅ React 管理后台 (基础结构)
│   ├── admin-bff/                  # ✅ 管理端 API (NestJS)
│   └── mobile-bff/                 # ✅ 移动端 API (NestJS)
├── infrastructure/docker/          # ✅ Docker 配置
├── scripts/                        # ✅ 开发脚本
├── docs/plan/                      # ✅ 规划文档
├── CLAUDE.md                       # ✅ 项目指导文档
├── HYBRID-DEV-SETUP.md            # ✅ 开发环境指南
├── PROJECT-STRUCTURE-2025-07-14.md # ✅ 目录结构文档
└── README.md                       # ✅ 项目说明
```

## 开发环境指南

### 🐳 推荐开发方式 - Docker 混合模式

```bash
# 启动混合开发环境
./scripts/hybrid-dev-start.sh

# 可选择的模式:
# 1. 完整环境 (数据库 + 所有服务)
# 2. 仅数据库 (PostgreSQL + Redis)
# 3. 开发工具 (pgAdmin + Redis Commander)
```

### 📱 Flutter 移动端开发

```bash
# 快速启动 Flutter Web 开发
./scripts/flutter-dev-start.sh

# 或手动启动
cd apps/mobile
fvm flutter run -d web-server --web-port=8080
```

### 🔧 必要的开发工具

**前置要求**:
- Node.js 18.19.0
- pnpm 8+
- Docker & Docker Compose
- FVM (Flutter Version Manager)

## 开发命令

### 基础项目管理
```bash
# 项目依赖管理
pnpm install                    # 安装依赖
pnpm run lint                   # 代码检查
pnpm run lint:fix              # 修复代码问题
pnpm run format                 # 格式化代码
pnpm run typecheck             # TypeScript 类型检查

# Docker 服务管理
./scripts/hybrid-dev-start.sh   # 启动混合开发环境
./scripts/hybrid-dev-stop.sh    # 停止开发环境
./scripts/hybrid-dev-status.sh  # 查看服务状态
```

### Flutter 开发命令
```bash
cd apps/mobile

# 开发相关
fvm flutter pub get            # 获取依赖
fvm flutter analyze            # 代码分析
fvm flutter build web          # 构建 Web 版本
fvm flutter test               # 运行测试

# 开发服务器
fvm flutter run -d web-server --web-port=8080  # 启动 Web 开发
```

## 架构和技术栈

### 当前技术架构
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **移动端**: Flutter 3.32.1 + go_router + 响应式设计
- **后端 API**: NestJS + TypeScript
- **管理后台**: React + Vite + React Admin
- **容器化**: Docker + Docker Compose

### 服务端口分配
| 服务 | 端口 | 用途 | 访问地址 |
|------|------|------|----------|
| **Flutter Web** | 8080 | 移动端开发 | http://localhost:8080 |
| **Admin SPA** | 3100 | 管理后台 | http://localhost:3100 |
| **Mobile BFF** | 3101 | 移动端 API | http://localhost:3101 |
| **Admin BFF** | 3102 | 管理端 API | http://localhost:3102 |
| **PostgreSQL** | 5432 | 主数据库 | localhost:5432 |
| **Redis** | 6379 | 缓存服务 | localhost:6379 |
| **pgAdmin** | 8182 | 数据库管理 | http://localhost:8182 |

## 下一步开发规划

### 🎯 核心功能开发 (高优先级)
1. **用户认证系统** - JWT + OAuth 2.0
2. **数据库设计** - PostgreSQL Schema + 迁移脚本
3. **基础 CRUD API** - 用户、技能、文档管理
4. **文档上传处理** - 支持 PDF 解析和向量化

### 🔧 技术增强
1. **状态管理** - Flutter BLoC 集成
2. **API 文档** - Swagger/OpenAPI 文档
3. **测试框架** - 单元测试 + 集成测试
4. **缓存策略** - Redis 缓存实现

### 🎨 用户体验优化
1. **页面动画** - Flutter 转场动画
2. **错误处理** - 统一错误处理和用户反馈
3. **响应式设计** - 适配各种屏幕尺寸
4. **离线支持** - 本地数据缓存

## 重要注意事项

### 开发原则
- **优先使用中文** - 所有交流和文档都使用中文
- **时间标准** - 使用当前系统时间生成文档 (2025-07-14)
- **渐进开发** - 从核心功能开始，逐步增加复杂性
- **代码质量** - 严格的 TypeScript 类型检查 + ESLint

### 文件管理
- **CLAUDE.md** - 项目指导文档，定期更新
- **PROJECT-STRUCTURE-2025-07-14.md** - 目录结构规范
- **HYBRID-DEV-SETUP.md** - 开发环境详细指南
- **NAVIGATION-IMPLEMENTATION-2025-07-14.md** - Flutter 导航实现记录

### 开发流程
1. 使用 TodoWrite 工具规划任务
2. 优先编辑现有文件，避免创建新文件
3. 完成功能后运行 lint 和 typecheck
4. 只有明确要求时才进行 git commit

---

**项目状态**: 🚀 基础架构完成，准备核心功能开发  
**最后更新**: 2025年7月14日  
**下次检查**: 根据开发进度更新