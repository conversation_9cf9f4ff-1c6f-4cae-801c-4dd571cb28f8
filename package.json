{"name": "masteryos", "version": "1.0.0", "description": "基于10,000小时法则的智能化技能培养与跟踪系统", "private": true, "scripts": {"dev": "echo '请先创建应用代码后再运行开发服务器'", "build": "echo '请先创建应用代码后再构建'", "test": "echo '请先创建应用代码后再运行测试'", "lint": "eslint . --ext .ts,.js,.json --ignore-path .gitignore", "lint:fix": "eslint . --ext .ts,.js,.json --ignore-path .gitignore --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "typecheck": "tsc --noEmit", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:connect": "psql -h localhost -p 8182 -U masteryos -d masteryos"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^5.3.2", "@types/node": "^20.10.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.5", "keywords": ["skill-tracking", "learning", "10000-hours", "typescript"], "author": "MasteryOS Team", "license": "MIT"}