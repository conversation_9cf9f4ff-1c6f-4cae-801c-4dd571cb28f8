{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "eslint.workingDirectories": ["packages/api", "packages/web", "packages/admin"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.formatOnSave": true, "editor.defaultFormatter": "vscode.typescript-language-features", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/build": true, "flutter-app/.dart_tool": true, "flutter-app/.packages": true, "flutter-app/build": true, "flutter-app/.flutter-plugins": true, "flutter-app/.flutter-plugins-dependencies": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/build": true, "**/coverage": true, "flutter-app/.dart_tool": true, "flutter-app/build": true}, "flutter.sdkPath": ".fvm/flutter_sdk", "dart.flutterSdkPath": ".fvm/flutter_sdk", "[dart]": {"editor.formatOnSave": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "yaml.schemas": {"https://json.schemastore.org/pubspec": "pubspec.yaml"}, "yaml.schemaStore.enable": false, "yaml.validate": true, "[yaml]": {"editor.defaultFormatter": "redhat.vscode-yaml"}, "cSpell.customDictionaries": {"project-words": {"name": "project-words", "path": "./.vscode/cspell.json", "addWords": true, "scope": "workspace"}}, "cSpell.enabled": true}