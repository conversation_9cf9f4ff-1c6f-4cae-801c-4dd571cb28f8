{"version": "0.2", "language": "en", "words": ["masteryos", "riverpod", "sqflite", "cupertino", "syncfusion", "formz", "Noto", "hexcode", "pubspec", "yaml", "flutter", "dart", "dio", "retrofit", "hive", "lottie", "intl", "timezone", "firebase", "crashlytics", "analytics", "websocket", "<PERSON><PERSON>", "devcontainer", "dockerfile", "postgres", "redis", "influxdb", "minio", "elasticsearch", "kibana", "prometheus", "jaeger", "<PERSON><PERSON><PERSON>", "nextjs", "pnpm", "fvm", "eslint", "tailwindcss", "vscode", "github", "macos", "linux", "windows", "android", "ios", "observablehq", "miniprogram", "psql", "pdev", "plint", "pfix", "ptypecheck", "direnv", "dotenv", "initdb", "ctype", "healthcheck", "isready", "appendonly", "xpack", "cogni", "fvmrc", "envrc", "venv", "pbuild", "ptest", "dcup", "dcdown", "dclogs", "frun", "fbuild", "ftest", "fclean", "fget", "pgpass", "d<PERSON><PERSON><PERSON>", "esbenp", "apollographql", "bradlc", "devcontainers", "moby", "orta", "firsttris", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtxr", "sqltools", "c<PERSON><PERSON>", "eamodio", "m<PERSON>tchie", "<PERSON><PERSON><PERSON><PERSON>", "githistory", "wayou", "gruntfuggly", "formulahendry", "kohler", "mikestead", "autofetch", "bashhistory", "commandhistory", "noninteractive", "iputils", "libstdc", "autoremove", "nodemon", "<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "pytest", "mypy", "numpy", "cmdline", "commandlinetools", "sdkmanager", "dearmor", "keyrings", "gith<PERSON><PERSON><PERSON>", "dpkg", "usermod", "histfile", "histsize", "savehist", "setopt", "rabbitmq", "addgroup", "adduser", "trae", "udemy", "toggl", "habitica", "duolingo", "wcag", "ccpa", "kloc", "AARRR", "ARPU", "Optimizely", "<PERSON><PERSON>", "gtag", "plpgsql", "Flink", "flink", "pyflink", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pyplot", "figsize", "savefig", "sklearn", "elif", "gantt", "shadcn", "Ollama", "Qdrant", "<PERSON><PERSON><PERSON><PERSON>", "Langchain", "Pinecone", "Weaviate", "GraphQL", "TailwindCSS", "Zustand", "Recharts", "Workbox", "Prisma", "TypeORM", "Apollo", "Kubernetes", "<PERSON><PERSON>", "ArgoCD", "MinIO", "Anthropic", "Gemini", "OpenAI", "WebSocket", "RBAC", "OAuth", "JWT", "WeChat", "generativelanguage", "<PERSON><PERSON>", "docling", "tsvector", "cpus", "pgvector", "pdfviewer", "screenutil", "VARCHAR", "Datagrid", "minioadmin"], "flagWords": [], "ignorePaths": ["node_modules/**", ".git/**", "dist/**", "build/**", ".next/**", "coverage/**", "*.lock", "*.log"]}