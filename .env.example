# ==========================================
# MasteryOS Environment Variables Template
# ==========================================
# Copy this file to .env and update values for your environment

# Node Environment
NODE_ENV=development
PORT=8180

# Database Configuration (MasteryOS 专用端口 8182)
DATABASE_URL=postgresql://masteryos:masteryos123@localhost:8182/masteryos
DATABASE_HOST=localhost
DATABASE_PORT=8182
DATABASE_NAME=masteryos
DATABASE_USER=masteryos
DATABASE_PASSWORD=masteryos123

# Redis Configuration (MasteryOS 专用端口 8183)
REDIS_URL=redis://localhost:8183
REDIS_HOST=localhost
REDIS_PORT=8183

# JWT Configuration (生产环境请使用强密码)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Development URLs (MasteryOS 专用端口)
NEXT_PUBLIC_API_URL=http://localhost:8181/api
NEXT_PUBLIC_APP_URL=http://localhost:8180

# Logging
LOG_LEVEL=debug