<!-- cSpell:words extname pgadmin pgvector -->
# MasteryOS 混合架构独立开发环境指南

**更新时间**: 2025年7月14日  
**架构版本**: 3.0 (Mobile+Web Hybrid)

## 🎯 环境概述

基于混合架构设计，提供完全独立的开发环境，支持：
- **移动端BFF**: 专注学习功能的NestJS API
- **管理端BFF**: 专注管理功能的NestJS API  
- **管理端SPA**: React + React-admin Web界面
- **Flutter应用**: 移动端开发环境
- **完整数据栈**: PostgreSQL + pgvector + Redis + MinIO

## 🚀 快速开始

### 1. 环境要求
```bash
# 系统要求
- Docker Desktop 4.0+
- Docker Compose 2.0+
- 8GB+ 内存
- 20GB+ 磁盘空间

# 验证环境
docker --version
docker-compose --version
```

### 2. 一键启动
```bash
# 启动完整开发环境
./scripts/hybrid-dev-start.sh

# 选择启动模式:
# 1) 完整环境 (推荐)
# 2) 调试模式 (包含管理工具)  
# 3) 仅移动端开发
# 4) 仅Web端开发
# 5) 包含本地AI服务
```

### 3. 验证环境
```bash
# 检查环境状态
./scripts/hybrid-dev-status.sh

# 查看服务日志
docker-compose -f docker-compose.hybrid-dev.yml logs -f
```

## 🏗️ 架构组件

### 核心服务

| 服务 | 端口 | 描述 | 访问地址 |
|------|------|------|----------|
| **admin-spa** | 3100 | 管理后台前端 | http://localhost:3100 |
| **mobile-bff** | 3101 | 移动端API | http://localhost:3101 |
| **admin-bff** | 3102 | 管理端API | http://localhost:3102 |
| **db** | 5432 | 主数据库 | postgres://postgres:password@localhost:5432/masteryos |
| **db-readonly** | - | 只读数据库 | 用于分析查询 |
| **redis** | 6379 | 缓存+队列 | redis://localhost:6379 |
| **minio** | 9000/9101 | 文件存储 | http://localhost:9101 |
| **docling** | 8080 | PDF处理 | http://localhost:8080 |

### 开发工具 (调试模式)

| 工具 | 端口 | 描述 | 访问地址 |
|------|------|------|----------|
| **redis-commander** | 8081 | Redis管理 | http://localhost:8081 |
| **pgAdmin** | 8182 | 数据库管理 | http://localhost:8182 |
| **ai-service** | 11434 | 本地AI模型 | http://localhost:11434 |
| **flutter-dev** | 8080 | Flutter开发 | http://localhost:8080 |

## 📁 项目结构

```
masteryos/
├── mobile-bff/                 # 移动端后端
│   ├── src/
│   │   ├── auth/              # 认证模块
│   │   ├── users/             # 用户管理
│   │   ├── documents/         # 文档处理
│   │   ├── ai/                # AI服务
│   │   └── main.ts
│   ├── Dockerfile.dev
│   └── package.json
├── admin-bff/                  # 管理端后端
│   ├── src/
│   │   ├── auth/              # 认证模块
│   │   ├── organizations/     # 组织管理
│   │   ├── users/             # 用户管理
│   │   ├── documents/         # 文档管理
│   │   ├── analytics/         # 数据分析
│   │   └── main.ts
│   ├── Dockerfile.dev
│   └── package.json
├── admin-spa/                  # 管理端前端
│   ├── src/
│   │   ├── components/        # 组件
│   │   ├── resources/         # React-admin资源
│   │   ├── providers/         # 数据提供者
│   │   └── App.tsx
│   ├── Dockerfile.dev
│   └── package.json
├── flutter-app/                # Flutter移动应用
│   ├── lib/
│   │   ├── core/              # 核心功能
│   │   ├── features/          # 功能模块
│   │   └── main.dart
│   ├── Dockerfile.dev
│   └── pubspec.yaml
├── scripts/                    # 开发脚本
│   ├── hybrid-dev-start.sh    # 启动环境
│   ├── hybrid-dev-stop.sh     # 停止环境
│   ├── hybrid-dev-clean.sh    # 清理环境
│   ├── hybrid-dev-status.sh   # 状态检查
│   ├── init-hybrid-db.sql     # 数据库初始化
│   └── seed-demo-data.sql     # 演示数据
├── config/
│   └── redis.conf             # Redis配置
├── docker-compose.hybrid-dev.yml  # 开发环境配置
└── HYBRID-DEV-SETUP.md        # 本文档
```

## 🛠️ 开发工作流

### 1. 数据库开发
```bash
# 连接主数据库
psql -h localhost -p 5432 -U postgres -d masteryos

# 使用pgAdmin Web界面
open http://localhost:8182
# 登录: <EMAIL> / admin123

# 检查pgvector扩展
SELECT * FROM pg_extension WHERE extname = 'vector';
```

### 2. 后端API开发
```bash
# 查看Mobile BFF日志
docker-compose -f docker-compose.hybrid-dev.yml logs -f mobile-bff

# 查看Admin BFF日志  
docker-compose -f docker-compose.hybrid-dev.yml logs -f admin-bff

# 进入容器开发
docker-compose -f docker-compose.hybrid-dev.yml exec mobile-bff bash
docker-compose -f docker-compose.hybrid-dev.yml exec admin-bff bash

# 安装依赖 (在容器内)
pnpm install
pnpm run start:dev
```

### 3. 前端开发
```bash
# 查看管理端前端日志
docker-compose -f docker-compose.hybrid-dev.yml logs -f admin-spa

# 进入前端容器
docker-compose -f docker-compose.hybrid-dev.yml exec admin-spa bash

# 安装依赖 (在容器内)
pnpm install  
pnpm run dev
```

### 4. Flutter移动端开发
```bash
# 启动Flutter开发环境
docker-compose -f docker-compose.hybrid-dev.yml --profile flutter-dev up -d flutter-dev

# 查看Flutter开发日志
docker-compose -f docker-compose.hybrid-dev.yml logs -f flutter-dev

# 访问Flutter Web开发服务器
open http://localhost:8080
```

### 5. 文件存储测试
```bash
# 访问MinIO控制台
open http://localhost:9101
# 登录: minioadmin / minioadmin123

# 测试文件上传API
curl -X POST http://localhost:3102/api/admin/documents/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@test.pdf"
```

## 🔧 常用命令

### 环境管理
```bash
# 启动环境
./scripts/hybrid-dev-start.sh

# 检查状态
./scripts/hybrid-dev-status.sh

# 停止环境  
./scripts/hybrid-dev-stop.sh

# 完全清理
./scripts/hybrid-dev-clean.sh
```

### 服务操作
```bash
# 重启特定服务
docker-compose -f docker-compose.hybrid-dev.yml restart mobile-bff
docker-compose -f docker-compose.hybrid-dev.yml restart admin-bff
docker-compose -f docker-compose.hybrid-dev.yml restart admin-spa

# 查看特定服务日志
docker-compose -f docker-compose.hybrid-dev.yml logs -f mobile-bff
docker-compose -f docker-compose.hybrid-dev.yml logs -f admin-bff

# 进入服务容器
docker-compose -f docker-compose.hybrid-dev.yml exec mobile-bff bash
docker-compose -f docker-compose.hybrid-dev.yml exec db psql -U postgres -d masteryos
```

### 数据操作
```bash
# 备份数据库
docker-compose -f docker-compose.hybrid-dev.yml exec db pg_dump -U postgres masteryos > backup.sql

# 恢复数据库
docker-compose -f docker-compose.hybrid-dev.yml exec -T db psql -U postgres masteryos < backup.sql

# 重置数据库
docker-compose -f docker-compose.hybrid-dev.yml down -v
./scripts/hybrid-dev-start.sh
```

## 🐛 故障排除

### 常见问题

**1. 端口冲突**
```bash
# 检查端口占用
lsof -i :3100
lsof -i :3101  
lsof -i :3102

# 停止冲突服务
./scripts/hybrid-dev-stop.sh
```

**2. 容器启动失败**
```bash
# 查看详细错误
docker-compose -f docker-compose.hybrid-dev.yml logs [服务名]

# 重新构建镜像
docker-compose -f docker-compose.hybrid-dev.yml build [服务名]
```

**3. 数据库连接失败**
```bash
# 检查数据库状态
docker-compose -f docker-compose.hybrid-dev.yml exec db pg_isready -U postgres

# 重启数据库
docker-compose -f docker-compose.hybrid-dev.yml restart db
```

**4. 数据卷权限问题**
```bash
# 清理并重建
./scripts/hybrid-dev-clean.sh
./scripts/hybrid-dev-start.sh
```

### 性能优化

**1. 内存不足**
```bash
# 调整Docker内存限制
# Docker Desktop -> Settings -> Resources -> Memory (建议8GB+)

# 监控资源使用
docker stats
```

**2. 磁盘空间不足**
```bash
# 清理Docker缓存
docker system prune -a

# 清理项目数据
./scripts/hybrid-dev-clean.sh
```

## 📊 环境监控

### 健康检查
```bash
# 完整状态检查
./scripts/hybrid-dev-status.sh

# 检查特定服务
docker-compose -f docker-compose.hybrid-dev.yml ps mobile-bff
docker-compose -f docker-compose.hybrid-dev.yml ps admin-bff

# 查看容器健康状态
docker inspect masteryos-hybrid-dev-db-1 --format='{{.State.Health.Status}}'
```

### 性能监控
```bash
# 实时资源监控
docker stats

# 查看数据卷使用情况
docker system df -v

# 网络连接检查
docker network inspect masteryos-hybrid-dev
```

## 🎯 下一步开发

### 1. 创建服务代码结构
```bash
# 创建Mobile BFF基础结构
mkdir -p mobile-bff/src/{auth,users,documents,ai,learning}

# 创建Admin BFF基础结构  
mkdir -p admin-bff/src/{auth,organizations,users,documents,analytics}

# 创建Admin SPA基础结构
mkdir -p admin-spa/src/{components,resources,providers}

# 创建Flutter应用结构
mkdir -p flutter-app/lib/{core,features,shared}
```

### 2. 配置开发工具
```bash
# 配置IDE数据库连接
# 主机: localhost
# 端口: 5432
# 用户: postgres  
# 密码: password
# 数据库: masteryos

# 配置Redis连接
# 主机: localhost
# 端口: 6379
```

### 3. API开发优先级
1. **认证系统**: JWT + 多租户
2. **用户管理**: CRUD + 权限
3. **文档管理**: 上传 + 处理 + 存储
4. **学习功能**: 进度追踪 + AI交互
5. **数据分析**: 仪表盘 + 报表

---

## 📚 相关文档

- [混合架构设计文档](docs/plan/hybrid-architecture-2025-07-14.md)
- [API接口文档](http://localhost:3101/docs) - Mobile BFF
- [管理端API文档](http://localhost:3102/docs) - Admin BFF
- [React-admin官方文档](https://marmelab.com/react-admin/)
- [Flutter开发文档](https://flutter.dev/docs)

**问题反馈**: 请在项目Issue中提交开发环境相关问题