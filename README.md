# MasteryOS | 万时通

**基于"一万小时定律"的智能化技能培养与跟踪系统**

> ✅ **项目状态**: 基础架构完成，Flutter 导航系统已实现，准备核心功能开发

## 📋 项目概述

MasteryOS 是一个专注于技能精通的学习平台，结合AI辅助、社交学习和游戏化机制，帮助用户通过科学方法在任何技能领域实现精通。

**已实现功能** (2025-07-14):
- ✅ **完整项目架构** - Monorepo 结构，清晰的目录组织
- ✅ **Flutter 移动应用** - 底部导航栏 + 路由系统 + 响应式设计
- ✅ **开发环境配置** - Docker 混合开发环境 + 多种启动脚本
- ✅ **API 服务基础** - NestJS 管理端和移动端 BFF 结构

**核心功能规划**:
- 🎯 **智能跟踪** - 精确记录技能练习时间和质量
- 📊 **数据洞察** - 学习模式分析和进度预测  
- 🎮 **游戏化** - 等级、成就系统激发学习动力
- 👥 **社交学习** - 学习伙伴匹配和社区互动
- 📚 **文档处理** - PDF 解析和向量化学习

## 🚀 快速开始

### 前置要求
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Node.js 18.19.0](https://nodejs.org/)
- [pnpm 8+](https://pnpm.io/)
- [FVM (Flutter Version Manager)](https://fvm.app/)

### 开发环境设置

**方式一：Docker 混合模式 (推荐)**
```bash
# 克隆项目
git clone https://github.com/changxiaoyangbrain/MasteryOS.git
cd MasteryOS

# 启动混合开发环境
./scripts/hybrid-dev-start.sh

# 选择开发模式:
# 1. 完整环境 (数据库 + 所有服务)
# 2. 仅数据库 (PostgreSQL + Redis)
# 3. 开发工具 (pgAdmin + Redis Commander)
```

**方式二：Flutter 移动端开发**
```bash
# 快速启动 Flutter Web 开发
./scripts/flutter-dev-start.sh

# 访问: http://localhost:8080
```

**方式三：本地开发**
```bash
# 安装依赖
pnpm install

# 启动数据库服务
./scripts/hybrid-dev-start.sh
# 选择模式 2 (仅数据库)

# 单独启动各个服务 (开发中)
cd apps/mobile && fvm flutter run -d web-server --web-port=8080
cd apps/admin-spa && pnpm run dev
```

## 🛠️ 技术栈

### 当前架构
- **移动端**: Flutter 3.32.1 + go_router + 响应式设计
- **管理后台**: React + Vite + React Admin
- **后端 API**: NestJS + TypeScript
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **容器化**: Docker + Docker Compose
- **版本管理**: FVM (Flutter), pnpm (Node.js)

### 🌐 服务端口配置
| 服务 | 端口 | 用途 | 访问地址 |
|------|------|------|----------|
| **Flutter Web** | 8080 | 移动端开发 | http://localhost:8080 |
| **Admin SPA** | 3100 | 管理后台 | http://localhost:3100 |
| **Mobile BFF** | 3101 | 移动端 API | http://localhost:3101 |
| **Admin BFF** | 3102 | 管理端 API | http://localhost:3102 |
| **PostgreSQL** | 5432 | 主数据库 | localhost:5432 |
| **Redis** | 6379 | 缓存服务 | localhost:6379 |
| **pgAdmin** | 8182 | 数据库管理 | http://localhost:8182 |
| **Redis Commander** | 8081 | Redis 管理 | http://localhost:8081 |

## 📝 开发命令

### 项目管理
```bash
# 依赖管理
pnpm install                    # 安装所有依赖
pnpm run lint                   # 代码检查
pnpm run lint:fix              # 修复代码问题
pnpm run format                 # 格式化代码
pnpm run typecheck             # TypeScript 类型检查

# 开发环境管理
./scripts/hybrid-dev-start.sh   # 启动混合开发环境
./scripts/hybrid-dev-stop.sh    # 停止开发环境
./scripts/hybrid-dev-status.sh  # 查看服务状态
./scripts/hybrid-dev-clean.sh   # 清理开发环境
```

### Flutter 开发
```bash
cd apps/mobile

# 开发相关
fvm flutter pub get            # 获取依赖
fvm flutter analyze            # 代码分析
fvm flutter build web          # 构建 Web 版本
fvm flutter test               # 运行测试

# 启动开发服务器
fvm flutter run -d web-server --web-port=8080
```

### Docker 服务
```bash
# 数据库服务
./scripts/hybrid-dev-start.sh   # 启动数据库
./scripts/hybrid-dev-logs.sh    # 查看日志

# 数据库连接 (pgAdmin)
open http://localhost:8182
# 登录: <EMAIL> / admin123
```

## 🗂️ 项目结构

```
masteryos/
├── apps/                           # 应用程序目录
│   ├── mobile/                     # ✅ Flutter 移动应用 (已实现导航)
│   │   ├── lib/                    # Dart 源代码
│   │   │   ├── core/               # 核心功能 (路由、主题、配置)
│   │   │   ├── features/           # 功能模块
│   │   │   │   ├── home/           # 首页 (含底部导航)
│   │   │   │   ├── documents/      # 文档页面
│   │   │   │   ├── learning/       # 学习页面
│   │   │   │   ├── profile/        # 个人资料页面
│   │   │   │   └── auth/           # 认证页面
│   │   │   └── main.dart           # 应用入口
│   │   ├── pubspec.yaml           # Flutter 依赖配置
│   │   └── web/                    # Web 资源
│   ├── admin-spa/                  # ✅ React 管理后台 (基础结构)
│   │   ├── src/                    # React 源代码
│   │   ├── package.json           # 前端依赖
│   │   └── vite.config.ts         # Vite 配置
│   ├── admin-bff/                  # ✅ 管理端 API (NestJS)
│   │   ├── src/                    # API 源代码
│   │   └── package.json           # 后端依赖
│   └── mobile-bff/                 # ✅ 移动端 API (NestJS)
│       ├── src/                    # API 源代码
│       └── package.json           # 后端依赖
├── infrastructure/                 # 基础设施配置
│   └── docker/                     # Docker 配置文件
│       ├── docker-compose.hybrid-dev.yml  # 混合开发环境
│       ├── docker-compose.mobile-dev.yml  # 移动端开发
│       └── docker-compose.yml             # 生产环境
├── scripts/                        # 开发脚本
│   ├── hybrid-dev-start.sh        # 混合开发启动
│   ├── flutter-dev-start.sh       # Flutter 开发启动
│   └── README.md                   # 脚本说明
├── docs/                           # 项目文档
│   └── plan/                       # 规划文档
├── .fvm/                           # Flutter 版本管理
├── .vscode/                        # VS Code 配置
├── CLAUDE.md                       # ✅ Claude Code 指导文档
├── HYBRID-DEV-SETUP.md            # ✅ 开发环境详细指南
├── PROJECT-STRUCTURE-2025-07-14.md # ✅ 目录结构文档
├── package.json                    # 项目根依赖
└── README.md                       # 本文件
```

## 🎯 下一步开发计划

### 🔥 核心功能开发 (高优先级)
1. **用户认证系统** - JWT + OAuth 2.0 集成
2. **数据库设计** - PostgreSQL Schema + 迁移脚本
3. **基础 CRUD API** - 用户、技能、文档管理接口
4. **文档上传处理** - PDF 解析和向量化处理

### 🔧 技术增强
1. **状态管理** - Flutter BLoC 模式集成
2. **API 文档** - Swagger/OpenAPI 自动生成
3. **测试框架** - 单元测试 + 集成测试
4. **缓存策略** - Redis 缓存实现

### 🎨 用户体验优化
1. **页面动画** - Flutter 转场动画效果
2. **错误处理** - 统一错误处理和用户反馈
3. **响应式设计** - 适配不同屏幕尺寸
4. **离线支持** - 本地数据缓存机制

### 🚀 部署和运维
1. **生产环境** - Docker 生产配置
2. **CI/CD 流水线** - 自动化构建和部署
3. **监控系统** - 日志收集和性能监控
4. **备份策略** - 数据备份和恢复方案

## 📋 开发状态

### ✅ 已完成 (2025-07-14)
- [x] 完整项目结构重组 (monorepo 架构)
- [x] Flutter 移动应用导航系统
- [x] Docker 混合开发环境配置
- [x] 开发脚本和工具链
- [x] 项目文档和指导文件
- [x] Git 仓库初始化和推送

### 🚧 开发中
- [ ] 用户认证和权限系统
- [ ] 数据库 Schema 设计
- [ ] API 接口实现
- [ ] 移动端业务逻辑

### 📅 计划中
- [ ] 管理后台功能完善
- [ ] 文档处理和 AI 集成
- [ ] 社交功能和游戏化系统
- [ ] 生产环境部署

## 🔗 相关链接

- **GitHub 仓库**: https://github.com/changxiaoyangbrain/MasteryOS.git
- **开发文档**: [HYBRID-DEV-SETUP.md](HYBRID-DEV-SETUP.md)
- **架构文档**: [PROJECT-STRUCTURE-2025-07-14.md](PROJECT-STRUCTURE-2025-07-14.md)
- **Claude 指导**: [CLAUDE.md](CLAUDE.md)

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！请参考 [CLAUDE.md](CLAUDE.md) 了解项目开发规范。

---

**🌟 让我们一起构建更好的技能学习体验！**

*最后更新: 2025年7月14日*