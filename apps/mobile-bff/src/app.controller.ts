import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('应用')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiResponse({ status: 200, description: '获取应用信息' })
  getInfo() {
    return this.appService.getInfo();
  }

  @Get('health')
  @ApiResponse({ status: 200, description: '健康检查' })
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'masteryos-admin-bff',
    };
  }
}