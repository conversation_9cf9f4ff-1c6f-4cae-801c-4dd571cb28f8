import { Ad<PERSON>, Resource, ShowGuesser, <PERSON><PERSON><PERSON><PERSON>, EditGuesser } from 'react-admin';
import { dataProvider } from './providers/dataProvider';
import { authProvider } from './providers/authProvider';
import { Dashboard } from './components/Dashboard';
import chineseMessages from 'ra-language-chinese';
import polyglotI18nProvider from 'ra-i18n-polyglot';

// 资源组件 (待实现)
import { UserList, UserEdit, UserCreate } from './resources/users';
import { DocumentList, DocumentEdit, DocumentCreate } from './resources/documents';
import { SkillList, SkillEdit, SkillCreate } from './resources/skills';
import { OrganizationList, OrganizationEdit, OrganizationCreate } from './resources/organizations';

const i18nProvider = polyglotI18nProvider(() => chineseMessages, 'zh');

function App() {
  return (
    <Admin
      dataProvider={dataProvider}
      authProvider={authProvider}
      i18nProvider={i18nProvider}
      dashboard={Dashboard}
      title="MasteryOS 管理后台"
    >
      {/* 组织管理 */}
      <Resource
        name="organizations"
        list={OrganizationList}
        edit={OrganizationEdit}
        create={OrganizationCreate}
        options={{ label: '组织管理' }}
      />
      
      {/* 用户管理 */}
      <Resource
        name="users"
        list={UserList}
        edit={UserEdit}
        create={UserCreate}
        options={{ label: '用户管理' }}
      />
      
      {/* 技能管理 */}
      <Resource
        name="skills"
        list={SkillList}
        edit={SkillEdit}
        create={SkillCreate}
        options={{ label: '技能管理' }}
      />
      
      {/* 文档管理 */}
      <Resource
        name="documents"
        list={DocumentList}
        edit={DocumentEdit}
        create={DocumentCreate}
        options={{ label: '文档管理' }}
      />
      
      {/* 学习会话 */}
      <Resource
        name="learning-sessions"
        list={ListGuesser}
        edit={EditGuesser}
        show={ShowGuesser}
        options={{ label: '学习记录' }}
      />
    </Admin>
  );
}

export default App;