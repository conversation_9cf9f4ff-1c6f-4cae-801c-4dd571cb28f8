{"name": "masteryos-admin-spa", "version": "1.0.0", "description": "MasteryOS 管理后台 - React Admin", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-admin": "^5.4.0", "react-router-dom": "^6.30.0", "@mui/material": "^6.4.0", "@mui/icons-material": "^6.4.0", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "ra-data-simple-rest": "^5.4.0", "ra-language-chinese": "^5.1.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "recharts": "^2.15.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.5", "@types/lodash": "^4.17.13", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "typescript": "^5.7.2", "vite": "^6.0.7"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}