{"name": "masteryos-admin-bff", "version": "1.0.0", "description": "MasteryOS 管理端 BFF API", "author": "MasteryOS Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/platform-express": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/swagger": "^8.0.7", "@nestjs/typeorm": "^10.0.2", "@nestjs/mapped-types": "^2.0.6", "typeorm": "^0.3.20", "pg": "^8.13.1", "redis": "^4.7.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.1", "class-transformer": "^0.5.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.0.4", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@nestjs/cli": "^10.4.17", "@nestjs/schematics": "^10.1.11", "@nestjs/testing": "^10.4.15", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/supertest": "^6.0.3", "@types/bcryptjs": "^2.4.6", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/multer": "^1.4.12", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}