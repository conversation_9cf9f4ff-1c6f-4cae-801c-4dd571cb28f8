version: '3.8'

services:
  # PostgreSQL 主数据库 - 核心必需
  postgres:
    image: postgres:15-alpine
    container_name: masteryos-postgres
    environment:
      POSTGRES_DB: masteryos
      POSTGRES_USER: masteryos
      POSTGRES_PASSWORD: masteryos123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "8182:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - masteryos-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U masteryos"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存 - 基础开发必需
  redis:
    image: redis:7-alpine
    container_name: masteryos-redis
    ports:
      - "8183:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - masteryos-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 以下服务暂时注释，可在需要时启用
  
  # # Elasticsearch 搜索引擎 - 当需要全文搜索时启用
  # elasticsearch:
  #   image: docker.elastic.co/elasticsearch/elasticsearch:8.11.1
  #   container_name: masteryos-elasticsearch
  #   environment:
  #     - discovery.type=single-node
  #     - xpack.security.enabled=false
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #   ports:
  #     - "9200:9200"
  #   volumes:
  #     - elasticsearch_data:/usr/share/elasticsearch/data
  #   networks:
  #     - masteryos-network
  #   restart: unless-stopped

  # # InfluxDB 时序数据库 - 当需要时序数据分析时启用
  # influxdb:
  #   image: influxdb:2.7-alpine
  #   container_name: masteryos-influxdb
  #   environment:
  #     DOCKER_INFLUXDB_INIT_MODE: setup
  #     DOCKER_INFLUXDB_INIT_USERNAME: masteryos
  #     DOCKER_INFLUXDB_INIT_PASSWORD: masteryos123
  #     DOCKER_INFLUXDB_INIT_ORG: masteryos
  #     DOCKER_INFLUXDB_INIT_BUCKET: metrics
  #   ports:
  #     - "8086:8086"
  #   volumes:
  #     - influxdb_data:/var/lib/influxdb2
  #   networks:
  #     - masteryos-network
  #   restart: unless-stopped

  # # MinIO 对象存储 - 当需要文件存储时启用
  # minio:
  #   image: minio/minio:latest
  #   container_name: masteryos-minio
  #   environment:
  #     MINIO_ROOT_USER: masteryos
  #     MINIO_ROOT_PASSWORD: masteryos123
  #   ports:
  #     - "9000:9000"
  #     - "9001:9001"
  #   volumes:
  #     - minio_data:/data
  #   command: server /data --console-address ":9001"
  #   networks:
  #     - masteryos-network
  #   restart: unless-stopped

networks:
  masteryos-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  # 需要时取消注释
  # elasticsearch_data:
  # influxdb_data:
  # minio_data: