version: '3.8'

services:
  # 开发容器 - 包含所有开发工具
  dev:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile.dev
    container_name: masteryos-dev
    volumes:
      - .:/workspace:cached
      - node_modules_cache:/workspace/node_modules
      - pnpm_cache:/home/<USER>/.pnpm-store
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*************************************************/masteryos
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=masteryos123
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=masteryos
      - MINIO_SECRET_KEY=masteryos123
      - DOCLING_SERVICE_URL=http://docling:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY:-}
    ports:
      - "8180:8180"  # Web Frontend
      - "8181:8181"  # API Backend
      - "3000:3000"  # Dev Server
      - "5000:5000"  # Debug Port
      - "9229:9229"  # Node.js Debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_started
      docling:
        condition: service_started
    networks:
      - masteryos-dev-network
    restart: unless-stopped
    stdin_open: true
    tty: true

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: masteryos-postgres-dev
    environment:
      POSTGRES_DB: masteryos
      POSTGRES_USER: masteryos
      POSTGRES_PASSWORD: masteryos123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "8182:5432"
    networks:
      - masteryos-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U masteryos"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: masteryos-redis-dev
    command: redis-server --appendonly yes --requirepass masteryos123
    volumes:
      - redis_dev_data:/data
    ports:
      - "8183:6379"
    networks:
      - masteryos-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "masteryos123", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: masteryos-minio-dev
    environment:
      MINIO_ROOT_USER: masteryos
      MINIO_ROOT_PASSWORD: masteryos123
    command: server /data --console-address ":9001"
    volumes:
      - minio_dev_data:/data
    ports:
      - "8185:9000"  # API
      - "8186:9001"  # Console
    networks:
      - masteryos-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Docling PDF 解析服务
  docling:
    image: docling/docling:latest
    container_name: masteryos-docling-dev
    environment:
      - MAX_FILE_SIZE=50MB
      - WORKERS=2
    volumes:
      - docling_dev_cache:/app/cache
    ports:
      - "8184:8000"
    networks:
      - masteryos-dev-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 负载均衡（可选）
  nginx:
    image: nginx:alpine
    container_name: masteryos-nginx-dev
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8080:80"
    depends_on:
      - dev
    networks:
      - masteryos-dev-network
    restart: unless-stopped

networks:
  masteryos-dev-network:
    driver: bridge
    name: masteryos-dev-network

volumes:
  postgres_dev_data:
    name: masteryos-postgres-dev-data
  redis_dev_data:
    name: masteryos-redis-dev-data
  minio_dev_data:
    name: masteryos-minio-dev-data
  docling_dev_cache:
    name: masteryos-docling-dev-cache
  node_modules_cache:
    name: masteryos-node-modules-cache
  pnpm_cache:
    name: masteryos-pnpm-cache