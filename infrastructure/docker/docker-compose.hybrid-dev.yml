version: '3.8'

services:
  # Mobile BFF (移动端API) - 专注学习功能
  mobile-bff:
    build:
      context: ../../apps/mobile-bff
      dockerfile: Dockerfile.dev
    ports:
      - "3101:3000"
    volumes:
      - ../../apps/mobile-bff:/app
      - mobile_node_modules:/app/node_modules
    depends_on:
      - db
      - redis
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/masteryos
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=mobile-dev-secret-2025
      - S3_ENDPOINT=http://minio:9000
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin
      - AI_API_BASE_URL=http://ai-service:8000
    command: pnpm run start:dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin BFF (管理端API) - 专注管理功能
  admin-bff:
    build:
      context: ../../apps/admin-bff
      dockerfile: Dockerfile.dev
    ports:
      - "3102:3000"
    volumes:
      - ../../apps/admin-bff:/app
      - admin_node_modules:/app/node_modules
    depends_on:
      - db
      - redis
      - db-readonly
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/masteryos
      - DATABASE_READONLY_URL=***********************************************/masteryos
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=admin-dev-secret-2025
      - S3_ENDPOINT=http://minio:9000
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin
      - DOCLING_SERVICE_URL=http://docling:8080
    command: pnpm run start:dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin SPA (管理端前端)
  admin-spa:
    build:
      context: ../../apps/admin-spa
      dockerfile: Dockerfile.dev
    ports:
      - "3100:3000"
    volumes:
      - ../../apps/admin-spa:/app
      - admin_spa_node_modules:/app/node_modules
    environment:
      - VITE_ADMIN_API_URL=http://localhost:3102
      - VITE_NODE_ENV=development
      - VITE_APP_TITLE=MasteryOS 管理后台
    command: pnpm run dev --host 0.0.0.0
    restart: unless-stopped
    depends_on:
      - admin-bff

  # PostgreSQL 主数据库 (with pgvector)
  db:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=masteryos
      - POSTGRES_INITDB_ARGS="--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../scripts/init-hybrid-db.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ../../scripts/seed-demo-data.sql:/docker-entrypoint-initdb.d/02-seed.sql
    command: >
      postgres -c max_connections=200
               -c shared_buffers=256MB
               -c effective_cache_size=1GB
               -c maintenance_work_mem=64MB
               -c checkpoint_completion_target=0.9
               -c wal_buffers=16MB
               -c default_statistics_target=100
               -c random_page_cost=1.1
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d masteryos"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL 只读副本 (用于分析查询)
  db-readonly:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=masteryos
    volumes:
      - postgres_readonly_data:/var/lib/postgresql/data
    command: >
      postgres -c max_connections=100
               -c shared_buffers=128MB
               -c effective_cache_size=512MB
    restart: unless-stopped
    depends_on:
      - db

  # Redis (缓存 + 任务队列)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # MinIO (S3兼容对象存储)
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9101:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
      - MINIO_DEFAULT_BUCKETS=masteryos-documents:public,masteryos-uploads:private
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Docling PDF 处理服务
  docling:
    image: ds4sd/docling:latest
    ports:
      - "8080:8080"
    environment:
      - DOCLING_PORT=8080
      - DOCLING_HOST=0.0.0.0
    volumes:
      - docling_cache:/app/cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI 服务 (可选，用于本地AI模型)
  ai-service:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    profiles:
      - ai-local

  # Flutter 开发环境
  flutter-dev:
    build:
      context: ../../apps/mobile
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"  # Flutter web dev server
    volumes:
      - ../../apps/mobile:/app
      - flutter_cache:/home/<USER>/.flutter
      - flutter_pub_cache:/home/<USER>/.pub-cache
    environment:
      - FLUTTER_WEB_PORT=8080
      - MOBILE_API_URL=http://mobile-bff:3000
    command: flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0
    profiles:
      - flutter-dev

  # Redis Commander (Redis 管理界面)
  redis-commander:
    image: ghcr.io/joeferner/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    profiles:
      - debug-tools

  # pgAdmin (数据库管理界面)
  pgadmin:
    image: dpage/pgadmin4:latest
    ports:
      - "8182:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    profiles:
      - debug-tools

volumes:
  postgres_data:
    driver: local
  postgres_readonly_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  docling_cache:
    driver: local
  ollama_data:
    driver: local
  mobile_node_modules:
    driver: local
  admin_node_modules:
    driver: local
  admin_spa_node_modules:
    driver: local
  flutter_cache:
    driver: local
  flutter_pub_cache:
    driver: local
  pgadmin_data:
    driver: local

networks:
  default:
    name: masteryos-hybrid-dev
    driver: bridge