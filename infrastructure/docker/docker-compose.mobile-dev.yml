version: '3.8'

services:
  # 统一的NestJS API服务
  api:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile.api
    ports:
      - "3000:3000"
    volumes:
      - .:/workspace
      - node_modules:/workspace/node_modules
    depends_on:
      - db
      - redis
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/masteryos
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-secret-key
    command: pnpm run start:dev

  # PostgreSQL with pgvector for AI embeddings
  db:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=masteryos
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis for caching and job queue
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # MinIO for file storage (S3-compatible)
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Flutter development environment
  flutter:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile.flutter
    volumes:
      - .:/workspace
      - flutter_cache:/home/<USER>/.flutter
    ports:
      - "8080:8080"  # Flutter web (development only)
    environment:
      - API_BASE_URL=http://api:3000
    profiles:
      - flutter-dev

volumes:
  postgres_data:
  redis_data:
  minio_data:
  node_modules:
  flutter_cache:

networks:
  default:
    name: masteryos-mobile-dev