import { backToTopPlugin } from '@vuepress/plugin-back-to-top'
import { mediumZoomPlugin } from '@vuepress/plugin-medium-zoom'
import { nprogressPlugin } from '@vuepress/plugin-nprogress'
import { searchPlugin } from '@vuepress/plugin-search'
import { defaultTheme } from '@vuepress/theme-default'
import { defineUserConfig } from 'vuepress'

export default defineUserConfig({
    // 站点配置
    lang: 'zh-CN',
    title: 'MasteryOS 开发文档',
    description: 'MasteryOS（万时通）项目的完整开发文档',

    // 基础路径
    base: '/docs/',

    // 头部配置
    head: [
        ['link', { rel: 'icon', href: '/favicon.ico' }],
        ['meta', { name: 'viewport', content: 'width=device-width,initial-scale=1,user-scalable=no' }]
    ],

    // 主题配置
    theme: defaultTheme({
        // 导航栏
        navbar: [
            {
                text: '首页',
                link: '/'
            },
            {
                text: '架构文档',
                children: [
                    '/architecture/overview.md',
                    '/architecture/technology-stack.md',
                    '/architecture/service-dependencies.md',
                    '/architecture/deployment-architecture.md'
                ]
            },
            {
                text: 'API文档',
                children: [
                    '/api/mobile-bff/',
                    '/api/admin-bff/',
                    '/api/data-models/',
                    '/api/authentication.md'
                ]
            },
            {
                text: '数据库',
                children: [
                    '/database/schema/',
                    '/database/migrations/',
                    '/database/queries/',
                    '/database/optimization.md'
                ]
            },
            {
                text: '开发指南',
                children: [
                    '/development/environment-setup.md',
                    '/development/coding-standards.md',
                    '/development/debugging-guide.md',
                    '/development/testing-guide.md'
                ]
            },
            {
                text: '部署运维',
                children: [
                    '/deployment/production-deployment.md',
                    '/deployment/monitoring.md',
                    '/deployment/backup-recovery.md',
                    '/deployment/troubleshooting.md'
                ]
            },
            {
                text: '用户指南',
                children: [
                    '/user-guides/quick-start.md',
                    '/user-guides/feature-guides/',
                    '/user-guides/faq.md',
                    '/user-guides/tutorials/'
                ]
            },
            {
                text: '项目管理',
                children: [
                    '/project-management/roadmap.md',
                    '/project-management/task-breakdown.md',
                    '/project-management/risk-management.md',
                    '/project-management/change-log.md'
                ]
            }
        ],

        // 侧边栏
        sidebar: {
            '/architecture/': [
                {
                    text: '架构文档',
                    children: [
                        '/architecture/overview.md',
                        '/architecture/technology-stack.md',
                        '/architecture/service-dependencies.md',
                        '/architecture/deployment-architecture.md'
                    ]
                }
            ],
            '/api/': [
                {
                    text: 'API 接口文档',
                    children: [
                        '/api/authentication.md'
                    ]
                },
                {
                    text: '移动端 BFF',
                    children: [
                        '/api/mobile-bff/'
                    ]
                },
                {
                    text: '管理端 BFF',
                    children: [
                        '/api/admin-bff/'
                    ]
                },
                {
                    text: '数据模型',
                    children: [
                        '/api/data-models/'
                    ]
                }
            ],
            '/database/': [
                {
                    text: '数据库文档',
                    children: [
                        '/database/optimization.md'
                    ]
                },
                {
                    text: '数据库模式',
                    children: [
                        '/database/schema/'
                    ]
                },
                {
                    text: '迁移脚本',
                    children: [
                        '/database/migrations/'
                    ]
                },
                {
                    text: '常用查询',
                    children: [
                        '/database/queries/'
                    ]
                }
            ],
            '/development/': [
                {
                    text: '开发文档',
                    children: [
                        '/development/environment-setup.md',
                        '/development/coding-standards.md',
                        '/development/debugging-guide.md',
                        '/development/testing-guide.md',
                        '/development/documentation-standards.md'
                    ]
                }
            ],
            '/deployment/': [
                {
                    text: '部署文档',
                    children: [
                        '/deployment/production-deployment.md',
                        '/deployment/monitoring.md',
                        '/deployment/backup-recovery.md',
                        '/deployment/troubleshooting.md'
                    ]
                }
            ],
            '/user-guides/': [
                {
                    text: '用户指南',
                    children: [
                        '/user-guides/quick-start.md',
                        '/user-guides/faq.md'
                    ]
                },
                {
                    text: '功能指南',
                    children: [
                        '/user-guides/feature-guides/'
                    ]
                },
                {
                    text: '使用教程',
                    children: [
                        '/user-guides/tutorials/'
                    ]
                }
            ],
            '/project-management/': [
                {
                    text: '项目管理',
                    children: [
                        '/project-management/roadmap.md',
                        '/project-management/task-breakdown.md',
                        '/project-management/risk-management.md',
                        '/project-management/change-log.md'
                    ]
                }
            ]
        },

        // 仓库配置
        repo: 'masteryos/masteryos',
        repoLabel: 'GitHub',
        docsDir: 'docs',
        docsBranch: 'main',
        editLink: true,
        editLinkText: '编辑此页',

        // 页面配置
        lastUpdated: true,
        lastUpdatedText: '最后更新',
        contributors: true,
        contributorsText: '贡献者',

        // 主题色
        colorMode: 'auto',
        colorModeSwitch: true
    }),

    // 插件配置
    plugins: [
        // 搜索插件
        searchPlugin({
            locales: {
                '/': {
                    placeholder: '搜索文档',
                }
            },
            maxSuggestions: 10,
            hotKeys: ['s', '/'],
            // 排除模板文件
            isSearchable: (page) => !page.path.includes('/.templates/')
        }),

        // 回到顶部
        backToTopPlugin(),

        // 图片缩放
        mediumZoomPlugin({
            selector: '.theme-default-content :not(a) > img',
            zoomOptions: {},
            delay: 300
        }),

        // 进度条
        nprogressPlugin()
    ],

    // Markdown 配置
    markdown: {
        code: {
            lineNumbers: true
        },
        toc: {
            level: [2, 3, 4]
        }
    },

    // 构建配置
    dest: 'dist',
    temp: '.temp',
    cache: '.cache'
})