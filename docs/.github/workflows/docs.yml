name: 构建和部署文档

on:
  push:
    branches: [ main, develop ]
    paths: [ 'docs/**' ]
  pull_request:
    branches: [ main ]
    paths: [ 'docs/**' ]

jobs:
  # 文档质量检查
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: docs/package-lock.json

      - name: 安装依赖
        run: |
          cd docs
          npm ci

      - name: Markdown 格式检查
        run: |
          cd docs
          npm run lint

      - name: 链接有效性检查
        run: |
          cd docs
          npm run check-links
        continue-on-error: true

  # 构建文档
  build:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: docs/package-lock.json

      - name: 安装依赖
        run: |
          cd docs
          npm ci

      - name: 构建文档
        run: |
          cd docs
          npm run build

      - name: 上传构建产物
        uses: actions/upload-artifact@v4
        with:
          name: docs-dist
          path: docs/dist/

  # 部署到 GitHub Pages (仅主分支)
  deploy:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    permissions:
      contents: read
      pages: write
      id-token: write
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: 下载构建产物
        uses: actions/download-artifact@v4
        with:
          name: docs-dist
          path: dist

      - name: 设置 Pages
        uses: actions/configure-pages@v4

      - name: 上传到 Pages
        uses: actions/upload-pages-artifact@v3
        with:
          path: dist

      - name: 部署到 GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4