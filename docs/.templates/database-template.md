---
title: "数据库表名"
version: "1.0.0"
lastUpdated: "YYYY-MM-DD"
author: "作者姓名"
reviewers: []
status: "draft"
tags:
  - "database"
  - "schema"
dependencies: []
---

# 数据库表: table_name

## 表概述

简要描述表的用途和业务含义。

## 表结构

### 基本信息

- **表名**: `table_name`
- **存储引擎**: InnoDB
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci

### 字段定义

| 字段名 | 数据类型 | 长度 | 允许NULL | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | 主键ID |
| name | VARCHAR | 255 | NO | - | 名称 |
| description | TEXT | - | YES | NULL | 描述 |
| status | TINYINT | 1 | NO | 1 | 状态(1:启用,0:禁用) |
| created_at | TIMESTAMP | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NO | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

## 索引设计

### 主键索引

```sql
PRIMARY KEY (`id`)
```

### 普通索引

```sql
KEY `idx_name` (`name`),
KEY `idx_status` (`status`),
KEY `idx_created_at` (`created_at`)
```

### 唯一索引

```sql
UNIQUE KEY `uk_name` (`name`)
```

### 复合索引

```sql
KEY `idx_status_created` (`status`, `created_at`)
```

## 约束关系

### 外键约束

```sql
CONSTRAINT `fk_table_name_user_id` 
FOREIGN KEY (`user_id`) 
REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE
```

### 检查约束

```sql
CONSTRAINT `chk_status` CHECK (`status` IN (0, 1))
```

## 触发器

### 更新时间触发器

```sql
CREATE TRIGGER `tr_table_name_updated_at`
BEFORE UPDATE ON `table_name`
FOR EACH ROW
SET NEW.updated_at = CURRENT_TIMESTAMP;
```

## 常用查询

### 基础查询

```sql
-- 查询所有启用状态的记录
SELECT * FROM table_name WHERE status = 1;

-- 按创建时间排序
SELECT * FROM table_name ORDER BY created_at DESC;
```

### 复杂查询

```sql
-- 统计查询
SELECT status, COUNT(*) as count 
FROM table_name 
GROUP BY status;

-- 关联查询
SELECT t.*, u.username 
FROM table_name t 
LEFT JOIN users u ON t.user_id = u.id;
```

## 性能优化

### 索引优化建议

- 为经常查询的字段添加索引
- 避免在小表上创建过多索引
- 定期分析索引使用情况

### 查询优化建议

- 使用 LIMIT 限制结果集大小
- 避免 SELECT * 查询
- 合理使用 WHERE 条件过滤

## 数据迁移

### 创建表语句

```sql
CREATE TABLE `table_name` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 数据迁移脚本

```sql
-- 迁移脚本示例
INSERT INTO table_name (name, description, status)
SELECT old_name, old_description, old_status
FROM old_table_name;
```

## 更新历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | YYYY-MM-DD | 作者姓名 | 初始版本 |