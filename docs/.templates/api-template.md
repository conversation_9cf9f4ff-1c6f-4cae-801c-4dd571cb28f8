---
title: "API接口名称"
version: "1.0.0"
lastUpdated: "YYYY-MM-DD"
author: "作者姓名"
reviewers: []
status: "draft"
tags:
  - "api"
  - "接口文档"
dependencies: []
---

# API接口名称

## 接口概述

简要描述接口的功能和用途。

## 接口信息

- **请求方法**: GET/POST/PUT/DELETE
- **请求路径**: `/api/v1/endpoint`
- **认证方式**: Bearer Token
- **权限要求**: 所需权限说明

## 请求参数

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 资源ID |

### 查询参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| limit | number | 否 | 10 | 每页数量 |

### 请求体

```json
{
  "field1": "string",
  "field2": 123,
  "field3": {
    "nestedField": "value"
  }
}
```

#### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| field1 | string | 是 | 字段1说明 |
| field2 | number | 否 | 字段2说明 |

## 响应格式

### 成功响应

**状态码**: 200

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "123",
    "name": "示例数据"
  }
}
```

### 错误响应

**状态码**: 400/401/403/404/500

```json
{
  "code": 1001,
  "message": "错误信息",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 1001 | 参数错误 |
| 1002 | 权限不足 |
| 1003 | 资源不存在 |

## 使用示例

### cURL 示例

```bash
curl -X GET \
  'https://api.masteryos.com/api/v1/endpoint?page=1&limit=10' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json'
```

### JavaScript 示例

```javascript
const response = await fetch('/api/v1/endpoint', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
```

## 注意事项

- 重要提醒和注意事项
- 使用限制说明
- 最佳实践建议

## 更新历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | YYYY-MM-DD | 作者姓名 | 初始版本 |