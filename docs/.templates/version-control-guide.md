# 文档版本控制指南

## 版本控制原则

### 语义化版本规范

文档版本号采用 `MAJOR.MINOR.PATCH` 格式：

- **MAJOR**: 重大结构变更或不兼容更新
- **MINOR**: 新增内容或功能，向后兼容
- **PATCH**: 错误修正、格式调整等小幅修改

### 版本更新触发条件

#### MAJOR 版本更新 (x.0.0)
- 文档结构重大调整
- 内容完全重写
- 不兼容的格式变更

#### MINOR 版本更新 (x.y.0)
- 新增章节或重要内容
- 新增功能说明
- 重要信息补充

#### PATCH 版本更新 (x.y.z)
- 错别字修正
- 格式调整
- 链接更新
- 小幅内容修改

## 文档状态管理

### 状态定义

- **draft**: 草稿状态，正在编写中
- **review**: 审查状态，等待审查
- **approved**: 已批准，可以发布
- **deprecated**: 已废弃，不再维护

### 状态流转

```
draft → review → approved
  ↓        ↓        ↓
deprecated ← deprecated ← deprecated
```

## 变更记录规范

### 记录格式

每个文档都应包含变更历史表格：

```markdown
## 更新历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.2.1 | 2025-01-06 | 张三 | 修正API示例错误 |
| 1.2.0 | 2025-01-05 | 李四 | 新增认证章节 |
| 1.1.0 | 2025-01-04 | 王五 | 补充错误码说明 |
| 1.0.0 | 2025-01-01 | 赵六 | 初始版本 |
```

### 变更说明规范

- 使用动词开头：新增、修改、删除、修正
- 简洁明了，突出重点
- 重大变更需要详细说明影响范围

## Git 工作流程

### 分支策略

- `main`: 主分支，包含已发布的稳定文档
- `develop`: 开发分支，用于集成新功能
- `feature/*`: 功能分支，用于开发新文档或重大更新
- `hotfix/*`: 热修复分支，用于紧急修复

### 提交规范

#### 提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明

- `docs`: 文档相关变更
- `feat`: 新增功能文档
- `fix`: 修复文档错误
- `style`: 格式调整
- `refactor`: 文档重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变更

#### 示例

```
docs(api): add user authentication endpoints

- Add login and logout API documentation
- Include JWT token handling examples
- Update error code definitions

Closes #123
```

### Pull Request 流程

1. 创建功能分支
2. 编写或修改文档
3. 本地测试和预览
4. 提交 Pull Request
5. 代码审查
6. 合并到目标分支

## 文档依赖管理

### 依赖声明

在文档 Front Matter 中声明依赖：

```yaml
dependencies:
  - "architecture/overview.md"
  - "api/authentication.md"
```

### 依赖更新策略

- 被依赖文档更新时，通知相关文档维护者
- 定期检查依赖关系的有效性
- 及时更新失效的依赖链接

## 发布管理

### 发布流程

1. 确认所有文档状态为 `approved`
2. 更新版本号和最后修改日期
3. 生成发布说明
4. 创建 Git 标签
5. 部署到文档网站

### 发布说明模板

```markdown
# 文档发布 v1.2.0

## 新增内容
- 新增用户认证API文档
- 补充数据库优化指南

## 更新内容
- 更新部署流程说明
- 完善错误处理章节

## 修复内容
- 修正API示例中的错误
- 更新失效链接

## 废弃内容
- 移除过时的配置说明
```

## 质量保证

### 自动化检查

- Markdown 格式检查
- 链接有效性验证
- 拼写检查
- 文档结构验证

### 人工审查

- 内容准确性检查
- 逻辑结构审查
- 语言表达审查
- 用户体验评估

## 工具支持

### 版本管理工具

- Git: 版本控制
- GitHub/GitLab: 协作平台
- Semantic Release: 自动化版本发布

### 文档工具

- GitBook: 文档网站生成
- VuePress: 静态网站生成器
- Docusaurus: 文档平台

### 自动化工具

- GitHub Actions: CI/CD 流程
- markdownlint: 格式检查
- link-checker: 链接检查