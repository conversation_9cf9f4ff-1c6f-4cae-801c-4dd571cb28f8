# MasteryOS 开发文档

欢迎来到 MasteryOS（万时通）项目的开发文档中心。本文档体系旨在为开发团队提供全面的项目信息和开发指南。

## 文档导航

### 📐 [架构文档](./architecture/)
- [系统架构概览](./architecture/overview.md) - 了解系统整体架构和设计原则
- [技术栈详解](./architecture/technology-stack.md) - 详细的技术栈说明
- [服务依赖关系](./architecture/service-dependencies.md) - 服务间依赖关系图
- [部署架构](./architecture/deployment-architecture.md) - 部署架构和环境配置

### 🔌 [API文档](./api/)
- [移动端BFF API](./api/mobile-bff/) - 移动端后端服务接口
- [管理端BFF API](./api/admin-bff/) - 管理端后端服务接口
- [数据模型](./api/data-models/) - 系统数据模型定义
- [认证机制](./api/authentication.md) - API认证和授权机制

### 🗄️ [数据库文档](./database/)
- [数据库模式](./database/schema/) - 数据库表结构和关系
- [迁移脚本](./database/migrations/) - 数据库版本迁移
- [常用查询](./database/queries/) - 常用SQL查询示例
- [性能优化](./database/optimization.md) - 数据库性能优化指南

### 💻 [开发文档](./development/)
- [环境配置](./development/environment-setup.md) - 开发环境搭建指南
- [代码规范](./development/coding-standards.md) - 代码编写规范
- [调试指南](./development/debugging-guide.md) - 调试工具和技巧
- [测试指南](./development/testing-guide.md) - 测试策略和方法

### 🚀 [部署文档](./deployment/)
- [生产部署](./deployment/production-deployment.md) - 生产环境部署指南
- [监控配置](./deployment/monitoring.md) - 系统监控和告警
- [备份恢复](./deployment/backup-recovery.md) - 数据备份和恢复
- [故障排除](./deployment/troubleshooting.md) - 常见问题排查

### 👥 [用户指南](./user-guides/)
- [快速入门](./user-guides/quick-start.md) - 新用户快速上手
- [功能指南](./user-guides/feature-guides/) - 详细功能使用说明
- [常见问题](./user-guides/faq.md) - 用户常见问题解答
- [教程](./user-guides/tutorials/) - 分步骤使用教程

### 📋 [项目管理](./project-management/)
- [项目路线图](./project-management/roadmap.md) - 项目发展规划
- [任务分解](./project-management/task-breakdown.md) - 详细任务分工
- [风险管理](./project-management/risk-management.md) - 项目风险识别和应对
- [变更记录](./project-management/change-log.md) - 项目变更历史

## 快速开始

如果你是新加入的开发者，建议按以下顺序阅读文档：

1. 📐 [系统架构概览](./architecture/overview.md) - 了解项目整体架构
2. 💻 [环境配置](./development/environment-setup.md) - 搭建开发环境
3. 💻 [代码规范](./development/coding-standards.md) - 了解编码标准
4. 🔌 [API文档](./api/) - 熟悉接口定义
5. 🗄️ [数据库文档](./database/) - 了解数据结构

## 文档维护

本文档体系采用版本控制管理，所有文档变更都会记录在Git历史中。如果发现文档错误或需要更新，请：

1. 创建新的分支
2. 修改相应文档
3. 提交Pull Request
4. 等待代码审查和合并

## 联系我们

如有任何问题或建议，请通过以下方式联系开发团队：

- 创建GitHub Issue
- 发送邮件至开发团队
- 在团队聊天群中讨论

---

*最后更新时间: 2025-01-04*