#!/bin/bash

# MasteryOS 文档开发脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 检查 Node.js 是否安装
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_message "错误: Node.js 未安装，请先安装 Node.js" $RED
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_message "错误: Node.js 版本过低，需要 16.x 或更高版本" $RED
        exit 1
    fi
    
    print_message "✓ Node.js 版本检查通过: $(node -v)" $GREEN
}

# 安装依赖
install_dependencies() {
    print_message "正在安装依赖..." $BLUE
    
    if [ ! -f "package-lock.json" ]; then
        npm install
    else
        npm ci
    fi
    
    print_message "✓ 依赖安装完成" $GREEN
}

# 运行格式检查
run_lint() {
    print_message "正在运行格式检查..." $BLUE
    
    if npm run lint; then
        print_message "✓ 格式检查通过" $GREEN
    else
        print_message "⚠ 格式检查发现问题，尝试自动修复..." $YELLOW
        npm run lint:fix
        print_message "✓ 自动修复完成" $GREEN
    fi
}

# 检查链接
check_links() {
    print_message "正在检查链接有效性..." $BLUE
    
    if npm run check-links; then
        print_message "✓ 链接检查通过" $GREEN
    else
        print_message "⚠ 发现失效链接，请检查并修复" $YELLOW
    fi
}

# 启动开发服务器
start_dev_server() {
    print_message "正在启动开发服务器..." $BLUE
    print_message "服务器将在 http://localhost:8080 启动" $GREEN
    print_message "按 Ctrl+C 停止服务器" $YELLOW
    
    npm run dev
}

# 构建文档
build_docs() {
    print_message "正在构建文档..." $BLUE
    
    npm run build
    
    print_message "✓ 文档构建完成，输出目录: dist/" $GREEN
}

# 显示帮助信息
show_help() {
    echo "MasteryOS 文档开发脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  dev      启动开发服务器 (默认)"
    echo "  build    构建生产版本"
    echo "  lint     运行格式检查"
    echo "  links    检查链接有效性"
    echo "  install  安装依赖"
    echo "  help     显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev     # 启动开发服务器"
    echo "  $0 build   # 构建文档"
    echo "  $0 lint    # 检查格式"
}

# 主函数
main() {
    # 切换到脚本所在目录的上级目录（docs目录）
    cd "$(dirname "$0")/.."
    
    case "${1:-dev}" in
        "dev")
            check_nodejs
            install_dependencies
            run_lint
            start_dev_server
            ;;
        "build")
            check_nodejs
            install_dependencies
            run_lint
            build_docs
            ;;
        "lint")
            check_nodejs
            install_dependencies
            run_lint
            ;;
        "links")
            check_nodejs
            install_dependencies
            check_links
            ;;
        "install")
            check_nodejs
            install_dependencies
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message "未知命令: $1" $RED
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"