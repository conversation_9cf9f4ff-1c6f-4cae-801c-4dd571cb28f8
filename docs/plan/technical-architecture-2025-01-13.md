# MasteryOS (万时通) 技术架构建议书

**日期**: 2025年1月14日  
**版本**: 1.3  
**作者**: 技术架构团队

## 1. 执行摘要

MasteryOS（万时通）是一个基于"一万小时定律"的智能技能发展和追踪系统。本文档提出了一个现代化、可扩展的技术架构方案，旨在支持系统从MVP到大规模商业化的平滑演进。

### 核心架构原则
- **技术栈统一**: 全栈TypeScript，减少复杂性和维护成本
- **渐进式架构**: 从单体应用起步，预留微服务化路径
- **API优先**: 统一的GraphQL API支持多端接入
- **事件驱动**: 支持实时功能和异步处理
- **云原生**: 容器化部署，弹性扩展
- **AI原生**: 基于API调用的智能功能集成

## 2. 系统架构概览

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                         客户端层                                 │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Web App (PWA) │   Mobile Apps   │      第三方集成             │
│   Next.js 14    │  React Native   │   API/SDK/Webhooks          │
└────────┬────────┴────────┬────────┴────────┬────────────────────┘
         │                 │                 │
         └─────────────────┴─────────────────┘
                           │
                    ┌──────▼──────┐
                    │ 负载均衡器   │
                    │   (Nginx)    │
                    └──────┬──────┘
                           │
┌──────────────────────────┴──────────────────────────────────────┐
│                      应用服务层                                  │
├─────────────┬──────────────┬──────────────┬────────────────────┤
│  核心服务   │   AI服务     │   实时服务   │    后台服务        │
│  (NestJS)   │  (NestJS)    │  (Socket.io) │   (Bull Queue)     │
│  - 用户认证 │  - 学习计划  │  - 实时提醒  │   - 数据同步       │
│  - 技能管理 │  - 智能评估  │  - 协作学习  │   - 报告生成       │
│  - 时间追踪 │  - API调用   │  - 实时排行  │   - 邮件通知       │
└─────┬───────┴──────┬───────┴──────┬───────┴────────┬───────────┘
      │              │              │                │
      └──────────────┴──────────────┴────────────────┘
                           │
┌──────────────────────────┴──────────────────────────────────────┐
│                      数据层                                      │
├─────────────┬──────────────┬─────────────────────────────────┤
│ PostgreSQL  │    Redis     │           MinIO                │
│  主数据库   │  缓存/会话   │         对象存储               │
│  全文搜索   │  消息队列    │        文档存储                │
└─────────────┴──────────────┴──────────────┴────────────────────┘
```

### 2.2 技术栈选型

#### 前端技术栈
- **Web应用**: Next.js 14 + React 18 + TypeScript
- **UI框架**: TailwindCSS + shadcn/ui
- **状态管理**: Zustand + React Query
- **图表可视化**: Recharts + D3.js
- **PWA支持**: Workbox + Service Workers

#### 后端技术栈
- **主框架**: NestJS + TypeScript
- **API层**: GraphQL (Apollo Server) + REST
- **认证**: JWT + Passport.js + OAuth 2.0
- **ORM**: Prisma (统一数据访问层)
- **消息队列**: Bull (Redis-based)
- **实时通信**: Socket.io
- **搜索**: PostgreSQL全文搜索 + GIN索引

#### AI服务技术栈
- **AI服务架构**: 集成到NestJS主后端 (统一TypeScript技术栈)
- **大模型API**: 
  - 主要: OpenAI GPT-4/GPT-3.5 API (使用官方 `openai` SDK)
  - 备选: Anthropic Claude API (`@anthropic-ai/sdk`)
  - 备选: Google Gemini API (`@google-ai/generativelanguage`)
  - 本地部署: Ollama (可选，作为备份方案)
- **AI编排工具**: 
  - LangChain.js (复杂AI工作流)
  - Vercel AI SDK (流式响应和UI集成)
- **向量数据库**: Pinecone (云服务) / Qdrant (自托管)
- **提示词管理**: TypeScript模板系统 + 配置管理

#### PDF文档处理技术栈
- **PDF解析服务**: Docling Docker容器 (独立微服务)
- **文档处理流程**: PDF → Docling服务 → Markdown → AI分析
- **服务通信**: NestJS HTTP客户端 ↔ Docling REST API
- **文档存储**: 
  - 原始PDF: MinIO对象存储 (Docker部署)
  - 解析Markdown: PostgreSQL TEXT字段
  - 文档元数据: PostgreSQL关系表
- **异步处理**: Bull Queue (处理大文件上传和解析)
- **容器编排**: Docker Compose (开发) / Kubernetes (生产)

#### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (生产环境)
- **反向代理**: Nginx (负载均衡 + SSL终止)
- **监控**: 
  - MVP阶段: 基础健康检查 + 应用日志
  - 生产阶段: Prometheus + Grafana (按需添加)
- **日志**: 
  - MVP阶段: 容器日志 + 文件日志
  - 生产阶段: 集中化日志管理 (按需添加)
- **CI/CD**: GitHub Actions + Docker Hub

## 3. 核心模块设计

### 3.1 用户认证与授权模块

```typescript
// 认证流程架构
interface AuthenticationFlow {
  providers: ['local', 'google', 'github', 'WeChat'];
  tokenStrategy: 'JWT';
  refreshToken: true;
  sessionManagement: 'Redis';
  roleBasedAccess: ['user', 'premium', 'enterprise', 'admin'];
}
```

**关键特性**:
- 多种登录方式支持
- JWT Token + Refresh Token机制
- 基于角色的访问控制(RBAC)
- 设备管理和会话控制

### 3.2 技能追踪核心模块

```typescript
// 技能追踪数据模型
interface SkillTracking {
  skill: {
    id: string;
    name: string;
    category: string;
    totalHours: number;
    level: SkillLevel;
  };
  session: {
    startTime: Date;
    endTime: Date;
    duration: number;
    quality: QualityAssessment;
    notes: string;
  };
  analytics: {
    dailyStats: TimeStats;
    weeklyTrends: TrendData;
    milestones: Achievement[];
  };
}
```

**核心功能**:
- 精确的时间记录（±1秒精度）
- 多维度质量评估
- 实时数据同步
- 离线数据支持

### 3.3 AI智能助手模块

```typescript
// AI助手服务架构 - 支持PDF文档处理
import { Injectable, HttpService } from '@nestjs/common';
import { OpenAI } from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { generateText } from 'ai';

@Injectable()
export class AILearningAssistant {
  private openai: OpenAI;
  private claude: Anthropic;
  
  constructor(
    private readonly vectorService: VectorService,
    private readonly promptService: PromptService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly documentService: DocumentService
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get('OPENAI_API_KEY')
    });
    this.claude = new Anthropic({
      apiKey: this.configService.get('ANTHROPIC_API_KEY')
    });
  }
  
  // 传统学习计划生成
  async generateLearningPlan(userProfile: UserProfile, skill: Skill, goals: Goal[]): Promise<LearningPlan> {
    const prompt = await this.promptService.buildLearningPlanPrompt(userProfile, skill, goals);
    
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        response_format: { type: 'json_object' }
      });
      return this.parseLearningPlan(response.choices[0].message.content);
    } catch (error) {
      return this.fallbackToClaude(prompt, 'learning-plan');
    }
  }

  // PDF文档学习计划生成
  async generateDocumentLearningPlan(
    document: Document, 
    userProfile: UserProfile, 
    skill: Skill
  ): Promise<DocumentLearningPlan> {
    // 获取Docling解析的Markdown内容
    const markdownContent = await this.documentService.getDocumentMarkdown(document.id);
    
    // 构建包含文档内容的提示
    const prompt = await this.promptService.buildDocumentLearningPlanPrompt(
      markdownContent, 
      userProfile, 
      skill
    );
    
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { 
            role: 'system', 
            content: '你是一个专业的学习规划师，请根据提供的文档内容生成结构化的学习计划。' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        response_format: { type: 'json_object' }
      });
      
      return this.parseDocumentLearningPlan(response.choices[0].message.content, document);
    } catch (error) {
      return this.fallbackToClaude(prompt, 'document-learning-plan');
    }
  }

  // 基于文档内容生成考核题目
  async generateDocumentQuestions(
    document: Document, 
    chapterIndex: number,
    difficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate'
  ): Promise<Question[]> {
    const markdownContent = await this.documentService.getChapterContent(document.id, chapterIndex);
    
    const prompt = await this.promptService.buildDocumentQuestionPrompt(
      markdownContent, 
      difficulty
    );
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '请根据文档内容生成10-20个考核题目，包含选择题、填空题和简答题。'
        },
        { role: 'user', content: prompt }
      ],
      temperature: 0.3,
      response_format: { type: 'json_object' }
    });
    
    return this.parseDocumentQuestions(response.choices[0].message.content);
  }

  // 文档学习进度评估
  async assessDocumentProgress(
    document: Document,
    userAnswers: UserAnswer[],
    learningTime: number
  ): Promise<DocumentProgressAssessment> {
    const markdownContent = await this.documentService.getDocumentMarkdown(document.id);
    
    const prompt = await this.promptService.buildDocumentAssessmentPrompt(
      markdownContent,
      userAnswers,
      learningTime
    );
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3
    });
    
    return this.parseDocumentAssessment(response.choices[0].message.content);
  }
  
  // 原有方法保持不变
  async assessProgress(practiceSessions: PracticeSession[]): Promise<ProgressAssessment> {
    const context = await this.vectorService.findSimilarSessions(practiceSessions);
    const prompt = await this.promptService.buildAssessmentPrompt(practiceSessions, context);
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3
    });
    
    return this.parseAssessment(response.choices[0].message.content);
  }
  
  async generateQuestions(skillLevel: string, recentLearnings: string[]): Promise<Question[]> {
    const prompt = await this.promptService.buildQuestionPrompt(skillLevel, recentLearnings);
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      response_format: { type: 'json_object' }
    });
    
    return this.parseQuestions(response.choices[0].message.content);
  }
  
  private async fallbackToClaude(prompt: string, type: string): Promise<any> {
    const response = await this.claude.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 1000,
      messages: [{ role: 'user', content: prompt }]
    });
    
    return this.parseResponse(response.content[0].text, type);
  }
}

// 文档处理服务
@Injectable()
export class DocumentService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService
  ) {}

  async parseDocument(fileBuffer: Buffer, fileName: string): Promise<Document> {
    // 调用Docling Docker服务
    const doclingUrl = this.configService.get('DOCLING_SERVICE_URL');
    
    const formData = new FormData();
    formData.append('file', fileBuffer, fileName);
    
    const response = await this.httpService.post(`${doclingUrl}/parse`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }).toPromise();
    
    const { markdown, metadata } = response.data;
    
    // 保存解析结果到数据库
    const document = await this.prisma.document.create({
      data: {
        fileName,
        originalContent: fileBuffer,
        markdownContent: markdown,
        metadata: metadata,
        status: 'processed'
      }
    });
    
    return document;
  }

  async getDocumentMarkdown(documentId: string): Promise<string> {
    const document = await this.prisma.document.findUnique({
      where: { id: documentId }
    });
    return document.markdownContent;
  }

  async getChapterContent(documentId: string, chapterIndex: number): Promise<string> {
    const markdown = await this.getDocumentMarkdown(documentId);
    // 解析章节内容逻辑
    return this.extractChapterFromMarkdown(markdown, chapterIndex);
  }
}

// AI模块配置
@Module({
  imports: [ConfigModule, HttpModule],
  providers: [
    AILearningAssistant,
    DocumentService,
    VectorService,
    PromptService,
    PrismaService
  ],
  exports: [AILearningAssistant, DocumentService]
})
export class AiModule {}
```

**AI功能特性**:
- **统一TypeScript实现**: 所有AI逻辑集成在NestJS主应用中
- **PDF文档智能处理**: Docling解析 + AI分析的完整流程
- **多供应商fallback策略**: OpenAI主用，Claude/Gemini备用
- **类型安全的API调用**: 使用官方TypeScript SDK
- **智能提示词模板系统**: 支持文档内容的上下文注入
- **向量数据库集成**: 支持语义搜索和文档相似度匹配
- **文档学习计划生成**: 基于Markdown内容的个性化计划
- **考核题目自动生成**: 根据文档章节内容生成多类型题目
- **团队协作学习**: 支持文档共享和团队进度跟踪
- **成本优化策略**: 智能模型选择和调用缓存
- **错误处理和重试机制**: 保证服务可靠性

### 3.4 社交学习模块

**功能设计**:
- 好友系统和学习小组
- 进度分享和互动
- 排行榜和挑战赛
- 知识分享社区

### 3.5 数据分析模块

**分析维度**:
- 个人学习数据分析
- 技能发展趋势
- 学习效率评估
- 群体学习模式分析

## 4. 数据库设计

### 4.1 核心数据表结构

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 技能表
CREATE TABLE skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name TEXT NOT NULL,
    category TEXT,
    description TEXT,
    total_hours DECIMAL(10,2) DEFAULT 0,
    current_level TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 练习会话表
CREATE TABLE practice_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    skill_id UUID REFERENCES skills(id),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration INTEGER, -- 秒数
    quality_score DECIMAL(3,2),
    focus_score DECIMAL(3,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 成就表
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    icon_url TEXT,
    points INTEGER DEFAULT 0,
    unlock_criteria JSONB
);

-- 用户成就关联表
CREATE TABLE user_achievements (
    user_id UUID REFERENCES users(id),
    achievement_id UUID REFERENCES achievements(id),
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, achievement_id)
);

-- 文档表
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    skill_id UUID REFERENCES skills(id),
    file_name TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    original_url TEXT, -- MinIO存储路径
    markdown_content TEXT, -- Docling解析的Markdown
    metadata JSONB, -- 文档元数据
    status TEXT DEFAULT 'processing', -- processing, processed, failed
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_time TIMESTAMP
);

-- 文档学习计划表
CREATE TABLE document_learning_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    user_id UUID REFERENCES users(id),
    plan_data JSONB NOT NULL, -- AI生成的学习计划
    chapters JSONB, -- 章节信息
    estimated_hours DECIMAL(10,2),
    difficulty_level TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文档学习进度表
CREATE TABLE document_learning_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_id UUID REFERENCES document_learning_plans(id),
    user_id UUID REFERENCES users(id),
    chapter_index INTEGER,
    chapter_name TEXT,
    completed BOOLEAN DEFAULT false,
    learning_time INTEGER DEFAULT 0, -- 秒数
    completion_date TIMESTAMP,
    notes TEXT
);

-- 文档考核题目表
CREATE TABLE document_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    chapter_index INTEGER,
    question_data JSONB NOT NULL, -- 题目内容和选项
    question_type TEXT, -- choice, fill, essay
    difficulty TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户答题记录表
CREATE TABLE user_answers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID REFERENCES document_questions(id),
    user_id UUID REFERENCES users(id),
    answer_data JSONB, -- 用户答案
    is_correct BOOLEAN,
    score DECIMAL(5,2),
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 团队文档学习表
CREATE TABLE team_document_learning (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    team_name TEXT NOT NULL,
    creator_id UUID REFERENCES users(id),
    invite_code TEXT UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 团队成员表
CREATE TABLE team_members (
    team_id UUID REFERENCES team_document_learning(id),
    user_id UUID REFERENCES users(id),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    role TEXT DEFAULT 'member', -- admin, member
    PRIMARY KEY (team_id, user_id)
);
```

### 4.2 索引策略

```sql
-- 性能优化索引
CREATE INDEX idx_skills_user_id ON skills(user_id);
CREATE INDEX idx_sessions_skill_id ON practice_sessions(skill_id);
CREATE INDEX idx_sessions_time ON practice_sessions(start_time, end_time);
CREATE INDEX idx_user_achievements ON user_achievements(user_id, unlocked_at);

-- 文档相关索引
CREATE INDEX idx_documents_user_skill ON documents(user_id, skill_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_document_progress_plan ON document_learning_progress(plan_id, user_id);

-- 全文搜索索引 (替代Elasticsearch)
CREATE INDEX idx_skills_search ON skills USING GIN(to_tsvector('simple', name || ' ' || description));
CREATE INDEX idx_documents_search ON documents USING GIN(to_tsvector('simple', file_name || ' ' || markdown_content));
```

## 5. API设计

### 5.1 GraphQL Schema

```graphql
type User {
  id: ID!
  email: String!
  username: String
  skills: [Skill!]!
  achievements: [Achievement!]!
  stats: UserStats!
}

type Skill {
  id: ID!
  name: String!
  category: String
  totalHours: Float!
  level: SkillLevel!
  sessions: [PracticeSession!]!
  analytics: SkillAnalytics!
}

type PracticeSession {
  id: ID!
  startTime: DateTime!
  endTime: DateTime
  duration: Int!
  quality: QualityAssessment!
  notes: String
}

type Query {
  me: User!
  skill(id: ID!): Skill
  mySkills(filter: SkillFilter): [Skill!]!
  leaderboard(skillId: ID!, timeRange: TimeRange): [LeaderboardEntry!]!
}

type Mutation {
  createSkill(input: CreateSkillInput!): Skill!
  startSession(skillId: ID!): PracticeSession!
  endSession(sessionId: ID!, quality: QualityInput!): PracticeSession!
  generateLearningPlan(skillId: ID!): LearningPlan!
}

type Subscription {
  sessionUpdates(skillId: ID!): PracticeSession!
  achievementUnlocked: Achievement!
}
```

### 5.2 REST API端点

```yaml
# 核心REST端点
/api/v1/auth/register    # POST - 用户注册
/api/v1/auth/login       # POST - 用户登录
/api/v1/auth/refresh     # POST - 刷新令牌
/api/v1/users/profile    # GET/PUT - 用户资料
/api/v1/skills           # GET/POST - 技能管理
/api/v1/sessions         # GET/POST - 会话管理
/api/v1/analytics        # GET - 数据分析
/api/v1/ai/plan          # POST - AI学习计划
/api/v1/ai/assess        # POST - AI进度评估
```

## 6. 部署架构

### 6.1 开发环境

```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    ports:
      - "8182:5432"
    environment:
      POSTGRES_DB: masteryos
      POSTGRES_USER: masteryos
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "8183:6379"
    volumes:
      - redis_data:/data

  api:
    build: ./apps/api
    ports:
      - "8181:3000"
    environment:
      DATABASE_URL: postgresql://masteryos:${DB_PASSWORD}@postgres:5432/masteryos
      REDIS_URL: redis://redis:6379
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY}
    depends_on:
      - postgres
      - redis

  web:
    build: ./apps/web
    ports:
      - "8180:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8181/graphql
    depends_on:
      - api

  docling:
    image: docling/docling:latest
    ports:
      - "8184:8000"
    environment:
      - MAX_FILE_SIZE=50MB
      - WORKERS=2
    volumes:
      - docling_cache:/app/cache
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  minio:
    image: minio/minio:latest
    ports:
      - "8185:9000"
      - "8186:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
      - api

volumes:
  postgres_data:
  redis_data:
  docling_cache:
  minio_data:
```

### 6.2 生产环境架构

```yaml
# Kubernetes部署示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: masteryos-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: masteryos-api
  template:
    metadata:
      labels:
        app: masteryos-api
    spec:
      containers:
      - name: api
        image: masteryos/api:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: masteryos-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## 7. 性能优化策略

### 7.1 缓存策略

```typescript
// Redis缓存层设计
class CacheService {
  // 用户数据缓存 - 5分钟
  async getUserData(userId: string) {
    const key = `user:${userId}`;
    return this.getOrSet(key, () => this.userService.findById(userId), 300);
  }

  // 技能统计缓存 - 1小时
  async getSkillStats(skillId: string) {
    const key = `skill:stats:${skillId}`;
    return this.getOrSet(key, () => this.analyticsService.calculateStats(skillId), 3600);
  }

  // 排行榜缓存 - 10分钟
  async getLeaderboard(skillId: string) {
    const key = `leaderboard:${skillId}`;
    return this.getOrSet(key, () => this.leaderboardService.generate(skillId), 600);
  }
}
```

### 7.2 数据库优化

- 使用数据库连接池
- 实施读写分离
- 定期数据归档
- 查询优化和索引调优

### 7.3 前端性能优化

- 代码分割和懒加载
- 图片优化和CDN加速
- Service Worker缓存
- 虚拟列表渲染

## 8. 安全架构

### 8.1 安全措施

```typescript
// 安全中间件配置
const securityConfig = {
  authentication: {
    jwt: {
      secret: process.env.JWT_SECRET,
      expiresIn: '15m',
      refreshExpiresIn: '7d'
    },
    oauth: {
      providers: ['google', 'github', 'WeChat']
    }
  },
  authorization: {
    rbac: true,
    policies: ['user', 'premium', 'enterprise', 'admin']
  },
  security: {
    helmet: true,
    cors: {
      origin: process.env.ALLOWED_ORIGINS?.split(','),
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100 // 限制100次请求
    }
  }
};
```

### 8.2 数据保护

- 敏感数据加密存储
- HTTPS强制使用
- SQL注入防护
- XSS/CSRF防护
- 数据备份和恢复策略

## 9. 监控和运维

### 9.1 监控体系

```yaml
# 监控指标
metrics:
  application:
    - API响应时间
    - 错误率
    - 并发用户数
    - 请求吞吐量
  business:
    - 日活跃用户(DAU)
    - 月度学习时长
    - 付费转化率
    - AI功能使用率
  infrastructure:
    - CPU/内存使用率
    - 数据库连接数
    - 缓存命中率
    - 磁盘I/O
```

### 9.2 日志管理

```typescript
// 结构化日志
logger.info('Practice session started', {
  userId: user.id,
  skillId: skill.id,
  sessionId: session.id,
  timestamp: new Date().toISOString()
});

// 错误追踪
logger.error('Payment processing failed', {
  error: error.message,
  stack: error.stack,
  userId: user.id,
  amount: payment.amount,
  context: 'subscription_renewal'
});
```

## 10. 扩展性设计

### 10.1 微服务演进路径

```
阶段1 (MVP): 模块化单体
├── 用户模块
├── 技能模块
├── AI模块
└── 分析模块

阶段2 (v2.0): 服务拆分
├── 用户服务
├── 技能服务
├── AI服务
├── 分析服务
└── 通知服务

阶段3 (v3.0): 完全微服务
├── 认证服务
├── 用户服务
├── 技能服务
├── 时间追踪服务
├── AI计划服务
├── AI评估服务
├── 分析服务
├── 社交服务
├── 支付服务
└── 通知服务
```

### 10.2 数据分片策略

```sql
-- 基于用户ID的水平分片
-- 分片键: user_id % 4
shard_0: user_id % 4 = 0
shard_1: user_id % 4 = 1
shard_2: user_id % 4 = 2
shard_3: user_id % 4 = 3
```

## 11. 开发流程和规范

### 11.1 Git工作流

```bash
# 分支策略
main          # 生产环境
├── develop   # 开发环境
    ├── feature/skill-tracking
    ├── feature/ai-assistant
    └── bugfix/session-timer

# 提交规范
feat: 添加AI学习计划生成功能
fix: 修复会话计时器精度问题
docs: 更新API文档
test: 添加技能模块单元测试
refactor: 重构认证中间件
```

### 11.2 代码规范

```typescript
// TypeScript配置
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true
  }
}
```

## 12. 成本估算

### 12.1 基础设施成本（月度）

```yaml
开发环境 (简化版):
  - 服务器: $50 (单台4核8G)
  - 数据库: $20 (PostgreSQL)
  - 缓存: $10 (Redis)
  - 总计: $80/月

生产环境（1000用户）:
  - 计算资源: $250 (K8s集群，简化配置)
  - 数据库: $120 (PostgreSQL RDS)
  - 缓存: $40 (Redis ElastiCache)
  - 对象存储: $20 (MinIO/S3)
  - CDN: $80
  - Docling服务: $60 (轻量部署)
  - 负载均衡: $25 (Nginx)
  - 总计: $595/月 (节省$155)

生产环境（10万用户）:
  - 计算资源: $2500 (优化配置)
  - 数据库: $1200 (高性能PostgreSQL)
  - 缓存: $400 (Redis集群)
  - 对象存储: $180 (大量PDF存储)
  - CDN: $800
  - AI服务: $2000
  - Docling服务: $300 (多实例优化)
  - 负载均衡: $100
  - 监控: $300 (按需添加)
  - 总计: $7800/月 (节省$1300)
```

### 12.2 第三方服务成本

```yaml
AI服务:
  - OpenAI API: $0.01-0.03/1K tokens (根据模型)
  - Anthropic Claude: $0.008-0.024/1K tokens
  - Google Gemini: $0.0005-0.002/1K tokens
  - 预计月度总成本: $500-3000 (取决于用户规模和使用频率)

邮件服务:
  - SendGrid: $15-80/月

短信服务:
  - Twilio: $50-200/月

支付服务:
  - Stripe: 2.9% + $0.30/笔
```

## 13. 风险管理

### 13.1 技术风险

| 风险 | 影响 | 缓解措施 |
|------|------|----------|
| AI API服务不稳定 | 高 | 多供应商自动fallback策略，统一错误处理机制 |
| 数据库性能瓶颈 | 高 | 读写分离，Redis缓存优化，数据分片 |
| 单一技术栈风险 | 中 | TypeScript生态成熟，团队技能集中，降低维护复杂度 |
| AI API成本控制 | 中 | 智能调用缓存，请求优化，成本监控告警 |
| 实时同步延迟 | 中 | 优化WebSocket，使用消息队列 |

### 13.2 业务风险

| 风险 | 影响 | 缓解措施 |
|------|------|----------|
| 用户增长缓慢 | 高 | 加强营销，优化用户体验 |
| 付费转化率低 | 高 | A/B测试，优化定价策略 |
| 竞争对手模仿 | 中 | 快速迭代，建立技术壁垒 |
| 数据安全事故 | 高 | 严格安全审计，数据加密 |

## 14. 实施路线图

### 14.1 第一阶段（3个月）- MVP开发

**月份1**:
- 搭建开发环境和CI/CD
- 完成用户认证模块
- 实现基础技能管理

**月份2**:
- 开发时间追踪功能
- 实现数据持久化
- 基础UI/UX开发

**月份3**:
- 集成基础分析功能
- 性能优化
- 内测和bug修复

### 14.2 第二阶段（3个月）- 核心功能

**月份4**:
- AI学习助手集成
- 智能计划生成
- 进度评估功能

**月份5**:
- 社交功能开发
- 游戏化元素
- 移动端适配

**月份6**:
- 支付系统集成
- 订阅管理
- 公测准备

### 14.3 第三阶段（6个月）- 商业化

**月份7-9**:
- 正式发布
- 营销推广
- 用户反馈迭代

**月份10-12**:
- 企业版开发
- API开放平台
- 国际化支持

## 15. 总结

MasteryOS技术架构设计基于统一TypeScript技术栈，遵循现代化、可扩展、高性能的原则，能够支撑产品从MVP到大规模商业化的发展需求。通过统一技术栈选择、合理的架构设计和完善的运维体系，我们有信心打造一个稳定、高效、维护性强的技能学习平台。

### 关键架构优势

1. **技术栈统一**: 全栈TypeScript减少复杂性，提高开发效率
2. **简化依赖**: 移除过度工程化组件，专注核心业务价值
3. **成本优化**: 简化架构节省20-25%基础设施成本
4. **AI原生集成**: 基于API调用的智能功能，避免过度工程化
5. **渐进式演进**: 支持从MVP到企业级的平滑扩展
6. **多供应商策略**: AI服务fallback机制保证可靠性
7. **端到端类型安全**: Prisma统一数据层，减少运行时错误
8. **运维简化**: 精简的技术栈降低学习和维护成本

### 下一步行动

1. 评审和完善技术架构设计
2. 搭建开发环境
3. 组建技术团队
4. 开始MVP开发
5. 制定详细的项目计划

---

**文档版本历史**:
- v1.0 (2025-01-13): 初始版本，完成整体架构设计
- v1.1 (2025-01-14): 统一TypeScript技术栈，移除Python依赖，优化AI模块设计
- v1.2 (2025-01-14): 集成PDF文档学习功能，添加Docling解析服务，完善文档存储架构
- v1.3 (2025-01-14): 架构简化优化，移除Elasticsearch和Kong，统一Prisma ORM，简化监控栈，优化成本结构