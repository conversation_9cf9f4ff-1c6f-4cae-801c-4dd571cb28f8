# MasteryOS 移动端优先技术架构 v2.0

**日期**: 2025年1月14日  
**版本**: 2.0 (Mobile-First)  
**作者**: 技术架构团队

## 📋 架构重新定位

### 战略转变
- **从**: Web+Mobile 全栈应用 → **到**: 移动端优先应用
- **从**: 复杂微服务架构 → **到**: 简化API服务
- **从**: React Native → **到**: Flutter
- **从**: 多服务部署 → **到**: 单一API容器

## 🎯 新技术栈

### 核心组件

| 层级 | 技术选择 | 替代方案 | 选择理由 |
|------|----------|----------|----------|
| **移动端** | Flutter | React Native | PDF性能优势、生态成熟度 |
| **API后端** | NestJS (单服务) | 多个微服务 | 简化部署、统一管理 |
| **数据库** | PostgreSQL + pgvector | PostgreSQL + 专用向量DB | 100ms延迟、基础设施统一 |
| **缓存队列** | Redis + BullMQ | 分离的消息队列 | 轻量化、功能完整 |
| **文件存储** | S3兼容存储 | 本地文件系统 | 可扩展性、安全性 |
| **部署** | PaaS (Render/Fly.io) | 自管理容器 | 运维简化、自动扩展 |

### PDF处理栈
```mermaid
graph TD
    A[Flutter PDF Viewer] --> B[Syncfusion SDK]
    B --> C[原生PDF渲染]
    D[用户上传PDF] --> E[NestJS API]
    E --> F[Docling转换]
    F --> G[Markdown内容]
    G --> H[pgvector存储]
    H --> I[AI检索增强]
```

## 🏗️ 系统架构

### 简化架构图
```
┌─────────────────────────────────────────┐
│              Flutter App                │
│          (iOS + Android)                │
└─────────────────┬───────────────────────┘
                  │ HTTPS/REST API
                  │
┌─────────────────▼───────────────────────┐
│           NestJS API Server             │
│  ┌─────────┬─────────┬─────────────────┐│
│  │  Auth   │   AI    │    Documents    ││
│  │ Module  │ Module  │     Module      ││
│  └─────────┴─────────┴─────────────────┘│
└─────────────────┬───────────────────────┘
                  │
        ┌─────────┴─────────┐
        │                   │
┌───────▼────────┐ ┌────────▼────────┐
│ PostgreSQL     │ │     Redis       │
│ + pgvector     │ │  + BullMQ       │
│ (数据+向量)     │ │  (缓存+队列)     │
└────────────────┘ └─────────────────┘
```

## 🚀 核心功能架构

### 1. PDF学习系统
```typescript
// Flutter客户端
class PDFLearningService {
  // 使用Syncfusion PDF Viewer
  Widget buildPDFViewer(String documentId) {
    return SfPdfViewer.network(
      'https://api.masteryos.com/documents/$documentId/view',
      headers: {'Authorization': 'Bearer $jwt'},
    );
  }
  
  // 保存标注到后端
  Future<void> saveAnnotation(DocumentAnnotation annotation) {
    return apiClient.post('/documents/${annotation.documentId}/annotations', 
                         data: annotation.toJson());
  }
}

// NestJS后端
@Controller('documents')
export class DocumentsController {
  @Post(':id/annotations')
  async saveAnnotation(
    @Param('id') documentId: string,
    @Body() annotation: CreateAnnotationDto
  ) {
    // 保存到PostgreSQL
    return this.documentsService.saveAnnotation(documentId, annotation);
  }
}
```

### 2. AI学习助手
```typescript
// AI处理流程
@Injectable()
export class AILearningService {
  async processDocumentForLearning(documentId: string) {
    // 1. 后台任务：PDF → Markdown
    const job = await this.queue.add('process-pdf', { documentId });
    
    // 2. 生成学习计划
    const learningPlan = await this.generateLearningPlan(documentId);
    
    return { jobId: job.id, learningPlan };
  }
  
  async searchSimilarContent(query: string): Promise<SearchResult[]> {
    // 使用pgvector进行语义搜索
    const embedding = await this.openai.createEmbedding(query);
    
    return this.database.query(`
      SELECT content, 1 - (embedding <=> $1) as similarity
      FROM document_chunks 
      WHERE 1 - (embedding <=> $1) > 0.8
      ORDER BY similarity DESC
      LIMIT 10
    `, [embedding]);
  }
}
```

## 📱 Flutter应用架构

### 项目结构
```
masteryos_app/
├── lib/
│   ├── core/
│   │   ├── auth/              # JWT认证管理
│   │   ├── network/           # API客户端
│   │   ├── storage/           # 本地数据缓存
│   │   └── constants/         # 应用常量
│   ├── features/
│   │   ├── auth/              # 登录注册
│   │   ├── dashboard/         # 主面板
│   │   ├── pdf_learning/      # PDF学习模块
│   │   │   ├── views/         # PDF查看器
│   │   │   ├── annotations/   # 标注功能
│   │   │   └── progress/      # 学习进度
│   │   ├── ai_chat/           # AI对话
│   │   └── profile/           # 用户设置
│   ├── shared/
│   │   ├── widgets/           # 共享组件
│   │   ├── models/            # 数据模型
│   │   └── utils/             # 工具函数
│   └── main.dart
├── android/                   # Android配置
├── ios/                      # iOS配置
└── pubspec.yaml              # 依赖管理
```

### 关键依赖
```yaml
dependencies:
  # PDF处理
  syncfusion_flutter_pdfviewer: ^24.2.9
  
  # 网络和状态管理
  dio: ^5.4.0
  riverpod: ^2.4.9
  
  # 本地存储
  drift: ^2.14.1
  shared_preferences: ^2.2.2
  
  # UI组件
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
```

## 🔧 开发环境

### Docker环境配置
创建了 `docker-compose.mobile-dev.yml`，包含：
- **api**: 统一的NestJS API服务
- **db**: PostgreSQL with pgvector
- **redis**: 缓存和任务队列
- **minio**: S3兼容文件存储
- **flutter**: Flutter开发环境（可选）

### 快速启动
```bash
# 启动后端开发环境
./scripts/mobile-dev-start.sh

# 或者手动启动
docker-compose -f docker-compose.mobile-dev.yml up -d

# 启动Flutter开发
docker-compose -f docker-compose.mobile-dev.yml --profile flutter-dev up flutter
```

## 📊 性能优化

### 1. 移动端优化
- **离线优先**: 使用Drift本地数据库缓存
- **图片优化**: CachedNetworkImage减少网络请求
- **PDF性能**: Syncfusion虚拟滚动，大文件流式加载

### 2. 后端优化
- **pgvector**: 100ms内语义搜索响应
- **BullMQ**: 异步处理长时间任务
- **Redis缓存**: API响应缓存，减少数据库查询

### 3. 网络优化
```typescript
// Flutter中的网络优化
class ApiClient {
  final Dio _dio = Dio();
  
  ApiClient() {
    // 请求重试
    _dio.interceptors.add(RetryInterceptor());
    
    // 缓存策略
    _dio.interceptors.add(DioCacheInterceptor());
    
    // JWT自动刷新
    _dio.interceptors.add(AuthInterceptor());
  }
}
```

## 🔄 迁移策略

### 阶段式迁移（Strangler Fig Pattern）

**阶段1: 并行开发（2-3周）**
- 创建 `feature/mobile-api-v2` 分支
- 重构NestJS为单一API服务
- 部署到独立环境 `api-v2.masteryos.com`

**阶段2: Flutter开发（4-6周）**
- Flutter应用完全基于新API开发
- 实现核心功能：PDF查看、AI对话、进度追踪
- 内测版本发布

**阶段3: 生产切换（1周）**
- 数据迁移脚本
- DNS切换到新API
- 旧系统下线

## 💰 成本优化

### 基础设施成本（月度估算）
- **PaaS部署**: $25-50 (单容器)
- **PostgreSQL**: $20-40 (托管数据库)
- **Redis**: $15-25 (托管缓存)
- **文件存储**: $10-20 (S3兼容)
- **总计**: $70-135/月（vs 之前$155/月）

### 开发成本
- **学习曲线**: Dart/Flutter 2-3周
- **迁移工作**: 4-6周全职开发
- **维护简化**: -50% 运维复杂度

## 🎯 下一步行动

### 立即执行
1. **技术验证**: Flutter PDF性能测试
2. **团队培训**: Dart/Flutter技能提升
3. **环境搭建**: 使用新的Docker配置

### 本周目标
1. **API重构**: 开始NestJS服务整合
2. **Flutter项目**: 创建基础项目结构
3. **数据库**: 测试pgvector性能

### 月度里程碑
- **Week 1-2**: 后端API重构完成
- **Week 3-4**: Flutter基础功能开发
- **Week 5-6**: PDF和AI功能集成
- **Week 7-8**: 测试和优化
- **Week 9**: 生产环境部署

---

## 📝 总结

通过这次架构重构，MasteryOS将从复杂的全栈Web应用转变为**专注移动端的高性能学习平台**。技术栈的简化不仅降低了运维复杂度和成本，更重要的是为核心功能（PDF学习、AI交互）提供了最佳的技术选择。

**核心优势**：
- ✅ **性能提升**: Flutter原生渲染 + pgvector快速检索
- ✅ **成本降低**: 简化部署减少30%基础设施成本  
- ✅ **开发效率**: 统一API + 现代移动端框架
- ✅ **用户体验**: 移动端优先的产品设计

这个架构为MasteryOS的移动端优先战略提供了坚实的技术基础。